"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, GripVertical, X } from "lucide-react"
import { useResume } from "@/context/resume-context"
import type { Project } from "@/types/resume"

export function ProjectsForm() {
  const { resumeData, updateProjects } = useResume()
  const [techInputs, setTechInputs] = useState<{ [key: string]: string }>({})

  const addProject = () => {
    const newProject: Project = {
      id: Date.now().toString(),
      name: "",
      description: "",
      technologies: [],
      liveUrl: "",
      githubUrl: "",
      highlights: [""],
    }
    updateProjects([...resumeData.projects, newProject])
  }

  const updateProject = (index: number, field: keyof Project, value: any) => {
    const updated = [...resumeData.projects]
    updated[index] = { ...updated[index], [field]: value }
    updateProjects(updated)
  }

  const removeProject = (index: number) => {
    const updated = resumeData.projects.filter((_, i) => i !== index)
    updateProjects(updated)
  }

  const addTechnology = (projectIndex: number, tech: string) => {
    if (!tech.trim()) return
    const updated = [...resumeData.projects]
    updated[projectIndex].technologies.push(tech.trim())
    updateProjects(updated)

    // Clear the input
    setTechInputs((prev) => ({ ...prev, [projectIndex]: "" }))
  }

  const removeTechnology = (projectIndex: number, techIndex: number) => {
    const updated = [...resumeData.projects]
    updated[projectIndex].technologies.splice(techIndex, 1)
    updateProjects(updated)
  }

  const addHighlight = (projectIndex: number) => {
    const updated = [...resumeData.projects]
    updated[projectIndex].highlights.push("")
    updateProjects(updated)
  }

  const updateHighlight = (projectIndex: number, highlightIndex: number, value: string) => {
    const updated = [...resumeData.projects]
    updated[projectIndex].highlights[highlightIndex] = value
    updateProjects(updated)
  }

  const removeHighlight = (projectIndex: number, highlightIndex: number) => {
    const updated = [...resumeData.projects]
    updated[projectIndex].highlights.splice(highlightIndex, 1)
    updateProjects(updated)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Projects</CardTitle>
        <Button onClick={addProject} size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Add Project
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {resumeData.projects.map((project, index) => (
          <div key={project.id} className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <GripVertical className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium">Project {index + 1}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeProject(index)}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label>Project Name</Label>
                <Input
                  placeholder="E-commerce Platform"
                  value={project.name}
                  onChange={(e) => updateProject(index, "name", e.target.value)}
                />
              </div>

              <div>
                <Label>Live URL</Label>
                <Input
                  placeholder="https://project-demo.com"
                  value={project.liveUrl}
                  onChange={(e) => updateProject(index, "liveUrl", e.target.value)}
                />
              </div>

              <div>
                <Label>GitHub URL</Label>
                <Input
                  placeholder="https://github.com/username/project"
                  value={project.githubUrl}
                  onChange={(e) => updateProject(index, "githubUrl", e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label>Description</Label>
              <Textarea
                placeholder="Brief description of the project and its purpose..."
                value={project.description}
                onChange={(e) => updateProject(index, "description", e.target.value)}
                className="min-h-[80px]"
              />
            </div>

            <div>
              <Label>Technologies Used</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {project.technologies.map((tech, techIndex) => (
                  <Badge key={techIndex} variant="secondary" className="flex items-center gap-1">
                    {tech}
                    <button onClick={() => removeTechnology(index, techIndex)} className="ml-1 hover:text-destructive">
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Add technology (e.g., React, Node.js)"
                  value={techInputs[index] || ""}
                  onChange={(e) => setTechInputs((prev) => ({ ...prev, [index]: e.target.value }))}
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      addTechnology(index, techInputs[index] || "")
                    }
                  }}
                />
                <Button variant="outline" onClick={() => addTechnology(index, techInputs[index] || "")}>
                  Add
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Key Highlights & Achievements</Label>
                <Button variant="outline" size="sm" onClick={() => addHighlight(index)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Highlight
                </Button>
              </div>

              {project.highlights.map((highlight, highlightIndex) => (
                <div key={highlightIndex} className="flex gap-2">
                  <Textarea
                    placeholder="• Implemented responsive design resulting in 40% increase in mobile user engagement"
                    value={highlight}
                    onChange={(e) => updateHighlight(index, highlightIndex, e.target.value)}
                    className="min-h-[60px]"
                  />
                  {project.highlights.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeHighlight(index, highlightIndex)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}

        {resumeData.projects.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No projects added yet.</p>
            <p className="text-sm">Click "Add Project" to showcase your work.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
