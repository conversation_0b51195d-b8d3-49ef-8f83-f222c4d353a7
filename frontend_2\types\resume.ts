export interface PersonalInfo {
  fullName: string
  email: string
  phone: string
  location: string
  linkedin: string
  github: string
  website: string
}

export interface ProfessionalSummary {
  summary: string
}

export interface TechnicalSkill {
  category: string
  skills: Array<{
    name: string
    proficiency: "Beginner" | "Intermediate" | "Advanced" | "Expert"
  }>
}

export interface WorkExperience {
  id: string
  jobTitle: string
  company: string
  location: string
  startDate: string
  endDate: string
  isCurrentRole: boolean
  responsibilities: string[]
}

export interface Project {
  id: string
  name: string
  description: string
  technologies: string[]
  liveUrl: string
  githubUrl: string
  highlights: string[]
}

export interface Education {
  id: string
  degree: string
  institution: string
  location: string
  graduationDate: string
  gpa: string
  relevantCoursework: string[]
}

export interface Certification {
  id: string
  name: string
  issuer: string
  dateObtained: string
  expirationDate: string
  credentialId: string
}

export interface ResumeData {
  personalInfo: PersonalInfo
  professionalSummary: ProfessionalSummary
  technicalSkills: TechnicalSkill[]
  workExperience: WorkExperience[]
  projects: Project[]
  education: Education[]
  certifications: Certification[]
}
