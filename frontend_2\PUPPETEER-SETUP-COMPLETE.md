# Complete Puppeteer PDF Setup Guide

## 📦 Installation

\`\`\`bash
npm install puppeteer
# or
yarn add puppeteer
# or
pnpm add puppeteer
\`\`\`

## 🚀 Features

- **Pixel-Perfect Accuracy**: Preview exactly matches PDF output
- **US Letter Format**: Exact 8.5" x 11" dimensions (816px × 1056px)
- **Professional Typography**: Times New Roman, proper spacing
- **Error Handling**: Comprehensive error recovery and user feedback
- **Security**: HTML escaping prevents XSS attacks
- **Performance**: Optimized browser configuration

## 🔧 How It Works

### 1. User Interaction
User fills out the resume form → clicks "Download PDF"

### 2. Data Processing
- Resume data is validated
- JSO<PERSON> is sent to `/api/generate-pdf`

### 3. Server-Side Rendering
- Puppeteer launches a headless Chrome browser
- HTML template is generated with user data
- <PERSON><PERSON><PERSON> renders the HTML with exact CSS styling

### 4. PDF Generation
- Puppeteer captures the rendered page as PDF
- Uses US Letter format with precise dimensions
- Returns PDF buffer to client

### 5. Download Process
- C<PERSON> receives PDF blob
- Creates download link dynamically
- Triggers automatic download

## 📁 File Structure

\`\`\`
app/
├── api/
│   └── generate-pdf/
│       └── route.ts          # Server-side PDF generation
components/
├── resume-template.tsx       # Shared template (preview + PDF)
├── resume-preview.tsx        # Browser preview
└── resizable-resume-builder.tsx # Main component
utils/
└── pdf-download-puppeteer.ts # Client-side download logic
\`\`\`

## 🛡️ Error Handling

- **Validation**: Checks for required fields
- **Server Errors**: Proper HTTP status codes
- **Network Issues**: Timeout and retry logic
- **User Feedback**: Toast notifications for all states
- **Logging**: Detailed console logs for debugging

## ⚡ Performance Optimizations

- **Browser Configuration**: Optimized Puppeteer launch args
- **Memory Management**: Proper browser cleanup
- **Timeout Handling**: Prevents hanging requests
- **CSS Optimization**: Minimal, efficient styles

## 🎨 Styling Consistency

- **Font**: Times New Roman (professional standard)
- **Sizes**: Exact pt-to-px conversion (11pt = 14.67px)
- **Layout**: Table-based layout for PDF compatibility
- **Colors**: Pure black (#000000) for best printing

## 🐛 Troubleshooting

### Common Issues:

1. **"Module not found: puppeteer"**
   - Solution: Run \`npm install puppeteer\`

2. **"PDF generation timeout"**
   - Solution: Increase timeout in API route

3. **"Empty PDF file"**
   - Solution: Check browser launch args

4. **"Font rendering issues"**
   - Solution: Ensure Times New Roman is available

### Debug Mode:
Add to API route for debugging:
\`\`\`typescript
const browser = await puppeteer.launch({
  headless: false, // Shows browser window
  devtools: true,  // Opens dev tools
  // ... other args
})
\`\`\`

## 🚀 Deployment

### Vercel
Works out of the box. Puppeteer is supported on Vercel.

### Docker
Add to Dockerfile:
\`\`\`dockerfile
RUN apt-get update && apt-get install -y \\
    chromium \\
    fonts-liberation
\`\`\`

### Other Platforms
Most cloud platforms support Puppeteer. Check their documentation for specific requirements.

## 📊 File Sizes

- **Average PDF size**: 50-150KB
- **With images**: 200-500KB
- **Text-only**: 30-80KB

## 🔐 Security

- **HTML Escaping**: All user input is escaped
- **No XSS**: Template prevents code injection
- **Server-side**: PDF generation isolated from client
- **No Storage**: No data is stored on server
\`\`\`
