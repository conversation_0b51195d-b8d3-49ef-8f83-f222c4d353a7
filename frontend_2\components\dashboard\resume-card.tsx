"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  MoreVertical,
  Edit,
  Copy,
  Trash2,
  Calendar,
  User,
} from "lucide-react"
import { useResume } from "@/context/resume-context"
import { useToast } from "@/hooks/use-toast"
import type { ResumeMetadata } from "@/types/resume"

interface ResumeCardProps {
  resume: ResumeMetadata
}

export function ResumeCard({ resume }: ResumeCardProps) {
  const router = useRouter()
  const { loadResume, duplicateCurrentResume, deleteResume } = useResume()
  const { toast } = useToast()
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleEdit = async () => {
    setIsLoading(true)
    try {
      const success = await loadResume(resume.id)
      if (success) {
        router.push("/d/canvas")
      } else {
        throw new Error("Failed to load resume")
      }
    } catch (error) {
      toast({
        title: "Load Failed",
        description: "Failed to load resume. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDuplicate = async () => {
    setIsLoading(true)
    try {
      // First load the resume, then duplicate it
      const loadSuccess = await loadResume(resume.id)
      if (loadSuccess) {
        const duplicatedId = await duplicateCurrentResume()
        if (duplicatedId) {
          toast({
            title: "Resume Duplicated",
            description: "Resume has been duplicated successfully.",
          })
        } else {
          throw new Error("Failed to duplicate resume")
        }
      } else {
        throw new Error("Failed to load resume for duplication")
      }
    } catch (error) {
      toast({
        title: "Duplication Failed",
        description: "Failed to duplicate resume. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    setIsLoading(true)
    try {
      const success = await deleteResume(resume.id)
      if (success) {
        toast({
          title: "Resume Deleted",
          description: "Resume has been deleted successfully.",
        })
        setIsDeleteDialogOpen(false)
      } else {
        throw new Error("Failed to delete resume")
      }
    } catch (error) {
      toast({
        title: "Deletion Failed",
        description: "Failed to delete resume. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  return (
    <>
      <div className="group relative bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200">
        {/* Resume Preview/Thumbnail */}
        <div 
          className="aspect-[8.5/11] bg-gray-50 rounded-t-lg border-b border-gray-200 cursor-pointer overflow-hidden"
          onClick={handleEdit}
        >
          <div className="w-full h-full flex items-center justify-center">
            {/* Simple preview placeholder - could be enhanced with actual resume preview */}
            <div className="w-3/4 h-5/6 bg-white border border-gray-200 rounded shadow-sm flex flex-col p-2">
              <div className="h-3 bg-gray-300 rounded mb-2"></div>
              <div className="h-2 bg-gray-200 rounded mb-1"></div>
              <div className="h-2 bg-gray-200 rounded mb-1"></div>
              <div className="h-2 bg-gray-200 rounded mb-2"></div>
              <div className="flex-1 space-y-1">
                <div className="h-1.5 bg-gray-100 rounded"></div>
                <div className="h-1.5 bg-gray-100 rounded"></div>
                <div className="h-1.5 bg-gray-100 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Resume Info */}
        <div className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 truncate mb-1">
                {resume.title}
              </h3>
              <div className="flex items-center text-sm text-gray-500 mb-1">
                <User className="w-3 h-3 mr-1" />
                <span className="truncate">{resume.preview.fullName || "No name"}</span>
              </div>
              <div className="flex items-center text-xs text-gray-400">
                <Calendar className="w-3 h-3 mr-1" />
                <span>Modified {formatDate(resume.updatedAt)}</span>
              </div>
            </div>

            {/* Actions Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-opacity h-8 w-8 p-0"
                  disabled={isLoading}
                >
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEdit} disabled={isLoading}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDuplicate} disabled={isLoading}>
                  <Copy className="w-4 h-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setIsDeleteDialogOpen(true)}
                  disabled={isLoading}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Loading overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Resume</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{resume.title}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
