"use client"

import type React from "react"

import { useState, useRef, useCallback, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Maximize2, Download, Loader2, GripVertical } from "lucide-react"
import { PersonalInfoForm } from "./forms/personal-info-form"
import { ProfessionalSummaryForm } from "./forms/professional-summary-form"
import { TechnicalSkillsForm } from "./forms/technical-skills-form"
import { WorkExperienceForm } from "./forms/work-experience-form"
import { ProjectsForm } from "./forms/projects-form"
import { EducationForm } from "./forms/education-form"
import { CertificationsForm } from "./forms/certifications-form"
import { ResumePreview } from "./resume-preview"
import { useResume } from "@/context/resume-context"
import { downloadResumePDF } from "@/utils/pdf-download-puppeteer"
import { useToast } from "@/hooks/use-toast"

export function ResizableResumeBuilder() {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const [leftWidth, setLeftWidth] = useState(50) // Percentage
  const [isDragging, setIsDragging] = useState(false)
  const [downloadProgress, setDownloadProgress] = useState("")
  const { resumeData } = useResume()
  const { toast } = useToast()
  const containerRef = useRef<HTMLDivElement>(null)

  const handleDownloadPDF = async () => {
    // Validate basic resume data
    if (!resumeData.personalInfo.fullName?.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter your full name before downloading.",
        variant: "destructive",
      })
      return
    }

    setIsDownloading(true)
    setDownloadProgress("Preparing PDF...")

    try {
      setDownloadProgress("Generating PDF on server...")

      const success = await downloadResumePDF(resumeData)

      if (success) {
        setDownloadProgress("Download complete!")
        toast({
          title: "Success!",
          description: "Your resume has been downloaded successfully.",
        })
      } else {
        throw new Error("Download returned false")
      }
    } catch (error) {
      console.error("PDF download error:", error)

      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"

      toast({
        title: "Download Failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsDownloading(false)
      setDownloadProgress("")
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !containerRef.current) return

      const containerRect = containerRef.current.getBoundingClientRect()
      const newLeftWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100

      // Constrain between 25% and 75%
      const constrainedWidth = Math.min(Math.max(newLeftWidth, 25), 75)
      setLeftWidth(constrainedWidth)
    },
    [isDragging],
  )

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Add event listeners for mouse move and up
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = "col-resize"
      document.body.style.userSelect = "none"
    } else {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = ""
      document.body.style.userSelect = ""
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = ""
      document.body.style.userSelect = ""
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 bg-white z-50 overflow-auto">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">Resume Preview</h2>
          <div className="flex gap-2">
            <Button onClick={handleDownloadPDF} variant="outline" disabled={isDownloading}>
              {isDownloading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {downloadProgress || "Generating PDF..."}
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </>
              )}
            </Button>
            <Button onClick={toggleFullscreen} variant="outline">
              Exit Fullscreen
            </Button>
          </div>
        </div>
        <div className="p-8 flex justify-center bg-gray-100">
          <ResumePreview />
        </div>
      </div>
    )
  }

  return (
    <div ref={containerRef} className="flex h-screen bg-gray-50 relative">
      {/* Left Panel - Forms */}
      <div className="bg-white border-r" style={{ width: `${leftWidth}%` }}>
        <div className="p-6 border-b">
          <h1 className="text-2xl font-bold text-gray-900">AI Tech Resume Builder</h1>
          <p className="text-gray-600 mt-1">Create your professional tech resume</p>
        </div>

        <ScrollArea className="h-[calc(100vh-120px)]">
          <div className="p-6 space-y-6">
            <PersonalInfoForm />
            <ProfessionalSummaryForm />
            <TechnicalSkillsForm />
            <WorkExperienceForm />
            <ProjectsForm />
            <EducationForm />
            <CertificationsForm />
          </div>
        </ScrollArea>
      </div>

      {/* Resizer */}
      <div
        className="w-2 bg-gray-300 hover:bg-blue-400 cursor-col-resize flex items-center justify-center relative group transition-colors duration-200 select-none"
        onMouseDown={handleMouseDown}
        style={{
          backgroundColor: isDragging ? "#3b82f6" : undefined,
          width: "8px",
        }}
      >
        <div className="absolute inset-y-0 -left-2 -right-2 flex items-center justify-center">
          <GripVertical className="w-4 h-4 text-gray-600 group-hover:text-white" />
        </div>
      </div>

      {/* Right Panel - Preview */}
      <div className="bg-gray-100" style={{ width: `${100 - leftWidth}%` }}>
        <div className="p-4 border-b bg-white flex justify-between items-center">
          <h2 className="text-lg font-semibold">Live Preview</h2>
          <div className="flex gap-2">
            <Button onClick={handleDownloadPDF} size="sm" disabled={isDownloading}>
              {isDownloading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {downloadProgress || "Generating..."}
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </>
              )}
            </Button>
            <Button onClick={toggleFullscreen} variant="outline" size="sm">
              <Maximize2 className="w-4 h-4 mr-2" />
              Fullscreen
            </Button>
          </div>
        </div>

        <ScrollArea className="h-[calc(100vh-80px)]">
          <div className="p-6 flex justify-center">
            <ResumePreview />
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}
