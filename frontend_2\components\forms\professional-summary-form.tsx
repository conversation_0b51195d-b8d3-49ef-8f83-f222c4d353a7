"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { useResume } from "@/context/resume-context"
import type { ProfessionalSummary } from "@/types/resume"

const summarySchema = z.object({
  summary: z.string(),
})

export function ProfessionalSummaryForm() {
  const { resumeData, updateProfessionalSummary } = useResume()

  const form = useForm<ProfessionalSummary>({
    resolver: zodResolver(summarySchema),
    defaultValues: resumeData.professionalSummary,
  })

  const handleSummaryChange = (value: string) => {
    updateProfessionalSummary({ summary: value })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Professional Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <FormField
            control={form.control}
            name="summary"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Summary</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Write a compelling professional summary that highlights your key skills, experience, and career objectives..."
                    className="min-h-[120px]"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e)
                      handleSummaryChange(e.target.value)
                    }}
                  />
                </FormControl>
                <FormMessage />
                <div className="text-sm text-muted-foreground mt-1">{field.value?.length || 0}/500 characters</div>
              </FormItem>
            )}
          />
        </Form>
      </CardContent>
    </Card>
  )
}
