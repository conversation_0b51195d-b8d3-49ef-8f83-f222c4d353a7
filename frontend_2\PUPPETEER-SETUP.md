# Puppeteer PDF Setup Instructions

## Required Dependencies

Install Puppeteer for server-side PDF generation:

\`\`\`bash
npm install puppeteer
# or
yarn add puppeteer
# or
pnpm add puppeteer
\`\`\`

## For Production Deployment

### Vercel Deployment
Add this to your `vercel.json`:

\`\`\`json
{
  "functions": {
    "app/api/generate-pdf/route.ts": {
      "maxDuration": 30
    }
  }
}
\`\`\`

### Environment Variables
No additional environment variables needed for basic functionality.

### Docker Deployment
If using Docker, add these dependencies to your Dockerfile:

\`\`\`dockerfile
RUN apt-get update && apt-get install -y \\
    wget \\
    ca-certificates \\
    fonts-liberation \\
    libasound2 \\
    libatk-bridge2.0-0 \\
    libdrm2 \\
    libgtk-3-0 \\
    libnspr4 \\
    libnss3 \\
    libxss1 \\
    libxtst6 \\
    xdg-utils
\`\`\`

## Features

- **Pixel-Perfect Accuracy**: Preview exactly matches PDF output
- **US Letter Format**: Exact 8.5" x 11" dimensions
- **Professional Typography**: Times New Roman, proper spacing
- **ATS-Friendly**: Clean, parseable format
- **Server-Side Generation**: Consistent across all devices
- **High Quality**: Vector-based text, not images

## Browser Compatibility

Works in all modern browsers. PDF generation happens server-side, so no browser limitations.

## Performance

- Initial PDF generation: ~2-3 seconds
- Subsequent generations: ~1-2 seconds
- File size: Typically 50-150KB (optimized)

## Troubleshooting

If you encounter issues:

1. **Memory errors**: Increase Node.js memory limit
2. **Timeout errors**: Increase API route timeout
3. **Font issues**: Ensure Times New Roman is available on server

## Security

- No user data is stored on server
- PDF generation is stateless
- All processing happens in isolated browser instances
