"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Plus } from "lucide-react"
import { useResume } from "@/context/resume-context"
import { useToast } from "@/hooks/use-toast"

export function CreateResumeCard() {
  const router = useRouter()
  const { createNewResume } = useResume()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const handleCreateNew = async () => {
    setIsLoading(true)
    try {
      const newResumeId = await createNewResume("New Resume")
      if (newResumeId) {
        toast({
          title: "Resume Created",
          description: "New resume created successfully. Redirecting to editor...",
        })
        router.push("/d/canvas")
      } else {
        throw new Error("Failed to create resume")
      }
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: "Failed to create new resume. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div
      onClick={handleCreateNew}
      className="group relative bg-white rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 cursor-pointer"
    >
      {/* Create New Area */}
      <div className="aspect-[8.5/11] flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 bg-gray-100 group-hover:bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 transition-colors">
            <Plus className="w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors" />
          </div>
          <p className="text-sm font-medium text-gray-600 group-hover:text-blue-700 transition-colors">
            Create New Resume
          </p>
        </div>
      </div>

      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      )}
    </div>
  )
}
