"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Plus, Trash2, GripVertical } from "lucide-react"
import { useResume } from "@/context/resume-context"
import type { WorkExperience } from "@/types/resume"

export function WorkExperienceForm() {
  const { resumeData, updateWorkExperience } = useResume()

  const addExperience = () => {
    const newExperience: WorkExperience = {
      id: Date.now().toString(),
      jobTitle: "",
      company: "",
      location: "",
      startDate: "",
      endDate: "",
      isCurrentRole: false,
      responsibilities: [""],
    }
    updateWorkExperience([...resumeData.workExperience, newExperience])
  }

  const updateExperience = (index: number, field: keyof WorkExperience, value: any) => {
    const updated = [...resumeData.workExperience]
    updated[index] = { ...updated[index], [field]: value }
    updateWorkExperience(updated)
  }

  const removeExperience = (index: number) => {
    const updated = resumeData.workExperience.filter((_, i) => i !== index)
    updateWorkExperience(updated)
  }

  const addResponsibility = (expIndex: number) => {
    const updated = [...resumeData.workExperience]
    updated[expIndex].responsibilities.push("")
    updateWorkExperience(updated)
  }

  const updateResponsibility = (expIndex: number, respIndex: number, value: string) => {
    const updated = [...resumeData.workExperience]
    updated[expIndex].responsibilities[respIndex] = value
    updateWorkExperience(updated)
  }

  const removeResponsibility = (expIndex: number, respIndex: number) => {
    const updated = [...resumeData.workExperience]
    updated[expIndex].responsibilities.splice(respIndex, 1)
    updateWorkExperience(updated)
  }

  const handleCurrentRoleChange = (index: number, checked: boolean) => {
    const updated = [...resumeData.workExperience]
    updated[index] = {
      ...updated[index],
      isCurrentRole: checked,
      endDate: checked ? "" : updated[index].endDate,
    }
    updateWorkExperience(updated)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Work Experience</CardTitle>
        <Button onClick={addExperience} size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Add Experience
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {resumeData.workExperience.map((experience, index) => (
          <div key={experience.id} className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <GripVertical className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium">Experience {index + 1}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeExperience(index)}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Job Title</Label>
                <Input
                  placeholder="Senior Software Engineer"
                  value={experience.jobTitle}
                  onChange={(e) => updateExperience(index, "jobTitle", e.target.value)}
                />
              </div>

              <div>
                <Label>Company</Label>
                <Input
                  placeholder="Tech Company Inc."
                  value={experience.company}
                  onChange={(e) => updateExperience(index, "company", e.target.value)}
                />
              </div>

              <div>
                <Label>Location</Label>
                <Input
                  placeholder="San Francisco, CA"
                  value={experience.location}
                  onChange={(e) => updateExperience(index, "location", e.target.value)}
                />
              </div>

              <div>
                <Label>Start Date</Label>
                <Input
                  type="month"
                  value={experience.startDate}
                  onChange={(e) => updateExperience(index, "startDate", e.target.value)}
                />
              </div>

              <div>
                <Label>End Date</Label>
                <Input
                  type="month"
                  value={experience.endDate}
                  onChange={(e) => updateExperience(index, "endDate", e.target.value)}
                  disabled={experience.isCurrentRole}
                  className={experience.isCurrentRole ? "bg-gray-100" : ""}
                />
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <Checkbox
                  id={`current-role-${experience.id}`}
                  checked={experience.isCurrentRole}
                  onCheckedChange={(checked) => handleCurrentRoleChange(index, checked as boolean)}
                />
                <Label htmlFor={`current-role-${experience.id}`} className="cursor-pointer">
                  Current Role
                </Label>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Key Responsibilities & Achievements</Label>
                <Button variant="outline" size="sm" onClick={() => addResponsibility(index)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Point
                </Button>
              </div>

              {experience.responsibilities.map((responsibility, respIndex) => (
                <div key={respIndex} className="flex gap-2">
                  <Textarea
                    placeholder="• Developed and maintained web applications using React and Node.js, resulting in 30% improved performance"
                    value={responsibility}
                    onChange={(e) => updateResponsibility(index, respIndex, e.target.value)}
                    className="min-h-[60px]"
                  />
                  {experience.responsibilities.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeResponsibility(index, respIndex)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}

        {resumeData.workExperience.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No work experience added yet.</p>
            <p className="text-sm">Click "Add Experience" to get started.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
