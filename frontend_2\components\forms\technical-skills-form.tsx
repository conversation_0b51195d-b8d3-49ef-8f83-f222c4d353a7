"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Plus, X, Trash2, Edit2 } from "lucide-react"
import { useResume } from "@/context/resume-context"

const suggestedCategories = [
  "Programming Languages",
  "Frontend Frameworks",
  "Backend Frameworks",
  "Databases",
  "Cloud Platforms",
  "DevOps Tools",
  "Testing Frameworks",
  "Mobile Development",
  "Data Science & Analytics",
  "Cybersecurity Tools",
  "Version Control",
  "Operating Systems",
  "Other Tools",
]

const proficiencyLevels = ["Beginner", "Intermediate", "Advanced", "Expert"] as const

export function TechnicalSkillsForm() {
  const { resumeData, updateTechnicalSkills } = useResume()
  const [newCategoryName, setNewCategoryName] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("")
  const [newSkill, setNewSkill] = useState({ name: "", proficiency: "Intermediate" as const })
  const [editingCategory, setEditingCategory] = useState<number | null>(null)
  const [editCategoryName, setEditCategoryName] = useState("")

  const addCategory = () => {
    if (!newCategoryName.trim()) return

    const currentSkills = [...resumeData.technicalSkills]
    const existingCategory = currentSkills.find((cat) => cat.category.toLowerCase() === newCategoryName.toLowerCase())

    if (!existingCategory) {
      currentSkills.push({
        category: newCategoryName.trim(),
        skills: [],
      })
      updateTechnicalSkills(currentSkills)
    }

    setNewCategoryName("")
  }

  const removeCategory = (categoryIndex: number) => {
    const currentSkills = [...resumeData.technicalSkills]
    currentSkills.splice(categoryIndex, 1)
    updateTechnicalSkills(currentSkills)
  }

  const editCategory = (categoryIndex: number, newName: string) => {
    if (!newName.trim()) return

    const currentSkills = [...resumeData.technicalSkills]
    currentSkills[categoryIndex].category = newName.trim()
    updateTechnicalSkills(currentSkills)
    setEditingCategory(null)
    setEditCategoryName("")
  }

  const addSkillToCategory = (categoryIndex: number) => {
    if (!newSkill.name.trim()) return

    const currentSkills = [...resumeData.technicalSkills]
    currentSkills[categoryIndex].skills.push({
      name: newSkill.name.trim(),
      proficiency: newSkill.proficiency,
    })

    updateTechnicalSkills(currentSkills)
    setNewSkill({ name: "", proficiency: "Intermediate" })
  }

  const removeSkill = (categoryIndex: number, skillIndex: number) => {
    const currentSkills = [...resumeData.technicalSkills]
    currentSkills[categoryIndex].skills.splice(skillIndex, 1)
    updateTechnicalSkills(currentSkills)
  }

  const getCategoryFilteredSuggestions = () => {
    const existingCategories = resumeData.technicalSkills.map((cat) => cat.category.toLowerCase())
    return suggestedCategories.filter(
      (cat) =>
        cat.toLowerCase().includes(newCategoryName.toLowerCase()) && !existingCategories.includes(cat.toLowerCase()),
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Technical Skills</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add New Category */}
        <div className="space-y-4">
          <div>
            <Label htmlFor="new-category">Add New Category</Label>
            <div className="flex gap-2 mt-1">
              <div className="flex-1">
                <Input
                  id="new-category"
                  placeholder="Enter category name (e.g., Programming Languages)"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      addCategory()
                    }
                  }}
                />
                {/* Category Suggestions */}
                {newCategoryName && getCategoryFilteredSuggestions().length > 0 && (
                  <div className="mt-2 p-2 border rounded-md bg-gray-50 max-h-32 overflow-y-auto">
                    <div className="text-xs text-gray-600 mb-1">Suggestions:</div>
                    <div className="flex flex-wrap gap-1">
                      {getCategoryFilteredSuggestions()
                        .slice(0, 5)
                        .map((suggestion) => (
                          <Button
                            key={suggestion}
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() => setNewCategoryName(suggestion)}
                          >
                            {suggestion}
                          </Button>
                        ))}
                    </div>
                  </div>
                )}
              </div>
              <Button onClick={addCategory} disabled={!newCategoryName.trim()}>
                <Plus className="w-4 h-4 mr-2" />
                Add Category
              </Button>
            </div>
          </div>
        </div>

        {/* Existing Categories */}
        <div className="space-y-4">
          {resumeData.technicalSkills.map((category, categoryIndex) => (
            <div key={categoryIndex} className="border rounded-lg p-4 space-y-3">
              {/* Category Header */}
              <div className="flex items-center justify-between">
                {editingCategory === categoryIndex ? (
                  <div className="flex items-center gap-2 flex-1">
                    <Input
                      value={editCategoryName}
                      onChange={(e) => setEditCategoryName(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === "Enter") {
                          editCategory(categoryIndex, editCategoryName)
                        }
                        if (e.key === "Escape") {
                          setEditingCategory(null)
                          setEditCategoryName("")
                        }
                      }}
                      className="flex-1"
                      autoFocus
                    />
                    <Button
                      size="sm"
                      onClick={() => editCategory(categoryIndex, editCategoryName)}
                      disabled={!editCategoryName.trim()}
                    >
                      Save
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setEditingCategory(null)
                        setEditCategoryName("")
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <>
                    <h4 className="font-semibold text-gray-800">{category.category}</h4>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setEditingCategory(categoryIndex)
                          setEditCategoryName(category.category)
                        }}
                      >
                        <Edit2 className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCategory(categoryIndex)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </>
                )}
              </div>

              {/* Skills in Category */}
              <div className="flex flex-wrap gap-2 min-h-[2rem]">
                {category.skills.map((skill, skillIndex) => (
                  <Badge key={skillIndex} variant="secondary" className="flex items-center gap-2">
                    {skill.name} ({skill.proficiency})
                    <button
                      onClick={() => removeSkill(categoryIndex, skillIndex)}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
                {category.skills.length === 0 && (
                  <span className="text-sm text-gray-500 italic">No skills added yet</span>
                )}
              </div>

              {/* Add Skill to Category */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                <Input
                  placeholder="Skill name (e.g., React)"
                  value={selectedCategory === category.category ? newSkill.name : ""}
                  onChange={(e) => {
                    setSelectedCategory(category.category)
                    setNewSkill({ ...newSkill, name: e.target.value })
                  }}
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      addSkillToCategory(categoryIndex)
                    }
                  }}
                />
                <Select
                  value={selectedCategory === category.category ? newSkill.proficiency : "Intermediate"}
                  onValueChange={(value: any) => {
                    setSelectedCategory(category.category)
                    setNewSkill({ ...newSkill, proficiency: value })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {proficiencyLevels.map((level) => (
                      <SelectItem key={level} value={level}>
                        {level}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={() => addSkillToCategory(categoryIndex)}
                  disabled={!newSkill.name.trim() || selectedCategory !== category.category}
                  size="sm"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Skill
                </Button>
              </div>
            </div>
          ))}
        </div>

        {resumeData.technicalSkills.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No skill categories added yet.</p>
            <p className="text-sm">Create a category first, then add skills to it.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
