"use client"

import { useResume } from "@/context/resume-context"
import { ResumeCard } from "./resume-card"
import { CreateResumeCard } from "./create-resume-card"

export function ResumeGrid() {
  const { getAllResumes } = useResume()
  const resumes = getAllResumes()

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      {/* Create New Resume Card */}
      <CreateResumeCard />
      
      {/* Existing Resume Cards */}
      {resumes.map((resume) => (
        <ResumeCard key={resume.id} resume={resume} />
      ))}
      
      {/* Empty State - Show when no resumes exist */}
      {resumes.length === 0 && (
        <div className="col-span-full flex flex-col items-center justify-center py-12 text-center">
          <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
            <svg
              className="w-12 h-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No resumes yet</h3>
          <p className="text-gray-500 mb-6 max-w-sm">
            Get started by creating your first professional resume. Click the "+" card above to begin.
          </p>
        </div>
      )}
    </div>
  )
}
