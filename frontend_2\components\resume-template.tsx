"use client"

import type { ResumeData } from "@/types/resume"

interface ResumeTemplateProps {
  resumeData: ResumeData
  isPDF?: boolean
}

export function ResumeTemplate({ resumeData, isPDF = false }: ResumeTemplateProps) {
  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    try {
      const date = new Date(dateString + "-01")
      return date.toLocaleDateString("en-US", { year: "numeric", month: "short" })
    } catch {
      return dateString
    }
  }

  // Exact conversion: 11pt = 14.67px at 96 DPI
  const styles = {
    container: {
      width: isPDF ? "8.5in" : "816px",
      minHeight: isPDF ? "11in" : "1056px",
      maxWidth: isPDF ? "8.5in" : "816px",
      margin: "0 auto",
      padding: isPDF ? "0.65in" : "62px",
      backgroundColor: "#ffffff",
      fontFamily: "'Times New Roman', Times, serif",
      fontSize: isPDF ? "11pt" : "14.67px",
      lineHeight: "1.15",
      color: "#000000",
      boxSizing: "border-box" as const,
    },
    header: {
      borderBottom: "2px solid #000000",
      paddingBottom: isPDF ? "12pt" : "16px",
      marginBottom: isPDF ? "16pt" : "21px",
    },
    name: {
      fontSize: isPDF ? "18pt" : "24px",
      fontWeight: "bold" as const,
      color: "#000000",
      margin: isPDF ? "0 0 8pt 0" : "0 0 11px 0",
      textAlign: "center" as const,
    },
    contact: {
      fontSize: isPDF ? "10pt" : "13.33px",
      color: "#000000",
      textAlign: "center" as const,
      marginBottom: isPDF ? "4pt" : "5px",
    },
    sectionHeader: {
      fontSize: isPDF ? "12pt" : "16px",
      fontWeight: "bold" as const,
      color: "#000000",
      margin: isPDF ? "16pt 0 8pt 0" : "21px 0 11px 0",
      borderBottom: "1px solid #000000",
      paddingBottom: "2px",
      textTransform: "uppercase" as const,
    },
    subsection: {
      marginBottom: isPDF ? "12pt" : "16px",
    },
    jobTitle: {
      fontSize: isPDF ? "11pt" : "14.67px",
      fontWeight: "bold" as const,
      color: "#000000",
      margin: "0",
    },
    company: {
      fontSize: isPDF ? "10pt" : "13.33px",
      color: "#000000",
      margin: "0",
      fontStyle: "italic" as const,
    },
    date: {
      fontSize: isPDF ? "10pt" : "13.33px",
      color: "#000000",
      fontWeight: "bold" as const,
    },
    bullet: {
      fontSize: isPDF ? "10pt" : "13.33px",
      color: "#000000",
      marginLeft: isPDF ? "20pt" : "27px",
      marginBottom: "2px",
      lineHeight: "1.2",
    },
    summary: {
      fontSize: isPDF ? "10pt" : "13.33px",
      color: "#000000",
      lineHeight: "1.3",
      margin: isPDF ? "0 0 16pt 0" : "0 0 21px 0",
      textAlign: "justify" as const,
    },
    skillCategory: {
      fontSize: isPDF ? "10pt" : "13.33px",
      fontWeight: "bold" as const,
      color: "#000000",
      margin: isPDF ? "0 0 4pt 0" : "0 0 5px 0",
    },
    skillList: {
      fontSize: isPDF ? "10pt" : "13.33px",
      color: "#000000",
      marginLeft: isPDF ? "20pt" : "27px",
      marginBottom: isPDF ? "8pt" : "11px",
    },
  }

  return (
    <div style={styles.container}>
      {/* Header */}
      <div style={styles.header}>
        <h1 style={styles.name}>{resumeData.personalInfo.fullName || "Your Name"}</h1>

        <div style={styles.contact}>
          {[resumeData.personalInfo.email, resumeData.personalInfo.phone, resumeData.personalInfo.location]
            .filter(Boolean)
            .join(" • ")}
        </div>

        {(resumeData.personalInfo.website || resumeData.personalInfo.linkedin || resumeData.personalInfo.github) && (
          <div style={styles.contact}>
            {[
              resumeData.personalInfo.website && resumeData.personalInfo.website.replace(/^https?:\/\//, ""),
              resumeData.personalInfo.linkedin && `LinkedIn: ${resumeData.personalInfo.linkedin}`,
              resumeData.personalInfo.github && `GitHub: ${resumeData.personalInfo.github}`,
            ]
              .filter(Boolean)
              .join(" • ")}
          </div>
        )}
      </div>

      {/* Professional Summary */}
      {resumeData.professionalSummary.summary && (
        <div>
          <h2 style={styles.sectionHeader}>Professional Summary</h2>
          <p style={styles.summary}>{resumeData.professionalSummary.summary}</p>
        </div>
      )}

      {/* Technical Skills */}
      {resumeData.technicalSkills.length > 0 && (
        <div>
          <h2 style={styles.sectionHeader}>Technical Skills</h2>
          {resumeData.technicalSkills.map((category, index) => (
            <div key={index} style={styles.subsection}>
              <div style={styles.skillCategory}>{category.category}:</div>
              <div style={styles.skillList}>
                {category.skills.map((skill, skillIndex) => `${skill.name} (${skill.proficiency})`).join(", ")}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Work Experience */}
      {resumeData.workExperience.length > 0 && (
        <div>
          <h2 style={styles.sectionHeader}>Work Experience</h2>
          {resumeData.workExperience.map((exp, index) => (
            <div key={exp.id} style={styles.subsection}>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "flex-start",
                  marginBottom: "4px",
                }}
              >
                <div style={{ flex: 1 }}>
                  <h3 style={styles.jobTitle}>{exp.jobTitle}</h3>
                  <p style={styles.company}>
                    {exp.company}, {exp.location}
                  </p>
                </div>
                <div style={styles.date}>
                  {formatDate(exp.startDate)} - {exp.isCurrentRole ? "Present" : formatDate(exp.endDate)}
                </div>
              </div>
              {exp.responsibilities
                .filter((resp) => resp.trim())
                .map((responsibility, respIndex) => (
                  <div key={respIndex} style={styles.bullet}>
                    • {responsibility}
                  </div>
                ))}
            </div>
          ))}
        </div>
      )}

      {/* Projects */}
      {resumeData.projects.length > 0 && (
        <div>
          <h2 style={styles.sectionHeader}>Projects</h2>
          {resumeData.projects.map((project, index) => (
            <div key={project.id} style={styles.subsection}>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "flex-start",
                  marginBottom: "4px",
                }}
              >
                <h3 style={styles.jobTitle}>{project.name}</h3>
                <div style={{ fontSize: isPDF ? "9pt" : "12px", color: "#000000" }}>
                  {project.liveUrl && <span>Live Demo</span>}
                  {project.liveUrl && project.githubUrl && <span> | </span>}
                  {project.githubUrl && <span>GitHub</span>}
                </div>
              </div>
              {project.description && (
                <p
                  style={{
                    fontSize: isPDF ? "10pt" : "13.33px",
                    color: "#000000",
                    margin: "2px 0 4px 0",
                    fontStyle: "italic" as const,
                  }}
                >
                  {project.description}
                </p>
              )}
              {project.technologies.length > 0 && (
                <p
                  style={{
                    fontSize: isPDF ? "9pt" : "12px",
                    color: "#000000",
                    margin: "2px 0 4px 0",
                    fontWeight: "bold" as const,
                  }}
                >
                  Technologies: {project.technologies.join(", ")}
                </p>
              )}
              {project.highlights
                .filter((highlight) => highlight.trim())
                .map((highlight, highlightIndex) => (
                  <div key={highlightIndex} style={styles.bullet}>
                    • {highlight}
                  </div>
                ))}
            </div>
          ))}
        </div>
      )}

      {/* Education */}
      {resumeData.education.length > 0 && (
        <div>
          <h2 style={styles.sectionHeader}>Education</h2>
          {resumeData.education.map((edu, index) => (
            <div key={edu.id} style={styles.subsection}>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "flex-start",
                  marginBottom: "4px",
                }}
              >
                <div style={{ flex: 1 }}>
                  <h3 style={styles.jobTitle}>{edu.degree}</h3>
                  <p style={styles.company}>
                    {edu.institution}, {edu.location}
                  </p>
                </div>
                <div style={styles.date}>
                  {formatDate(edu.graduationDate)}
                  {edu.gpa && (
                    <div style={{ fontSize: isPDF ? "9pt" : "12px", fontWeight: "normal" }}>GPA: {edu.gpa}</div>
                  )}
                </div>
              </div>
              {edu.relevantCoursework.length > 0 && (
                <p
                  style={{
                    fontSize: isPDF ? "10pt" : "13.33px",
                    color: "#000000",
                    margin: "4px 0 0 0",
                  }}
                >
                  <strong>Relevant Coursework:</strong> {edu.relevantCoursework.join(", ")}
                </p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Certifications */}
      {resumeData.certifications.length > 0 && (
        <div>
          <h2 style={styles.sectionHeader}>Certifications</h2>
          {resumeData.certifications.map((cert, index) => (
            <div key={cert.id} style={styles.subsection}>
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
                <div style={{ flex: 1 }}>
                  <h3 style={styles.jobTitle}>{cert.name}</h3>
                  <p style={styles.company}>{cert.issuer}</p>
                  {cert.credentialId && (
                    <p
                      style={{
                        fontSize: isPDF ? "9pt" : "12px",
                        color: "#000000",
                        margin: "0",
                      }}
                    >
                      Credential ID: {cert.credentialId}
                    </p>
                  )}
                </div>
                <div style={styles.date}>
                  {formatDate(cert.dateObtained)}
                  {cert.expirationDate && (
                    <div style={{ fontSize: isPDF ? "9pt" : "12px", fontWeight: "normal" }}>
                      Expires: {formatDate(cert.expirationDate)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
