"use client"

import type { ResumeData } from "@/types/resume"

interface ResumeTemplateProps {
  resumeData: ResumeData
  isPDF?: boolean
}

export function ResumeTemplate({ resumeData, isPDF = false }: ResumeTemplateProps) {
  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    try {
      const date = new Date(dateString + "-01")
      return date.toLocaleDateString("en-US", { year: "numeric", month: "short" })
    } catch {
      return dateString
    }
  }

  // Modern two-column design styles
  const styles = {
    container: {
      width: isPDF ? "8.5in" : "816px",
      minHeight: isPDF ? "11in" : "1056px",
      maxWidth: isPDF ? "8.5in" : "816px",
      margin: "0 auto",
      backgroundColor: "#ffffff",
      fontFamily: "'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
      fontSize: isPDF ? "10pt" : "13px",
      lineHeight: "1.4",
      color: "#333333",
      boxSizing: "border-box" as const,
      display: "flex",
      padding: "0",
    },
    // Left column (main content)
    leftColumn: {
      width: "65%",
      padding: isPDF ? "0.5in" : "40px",
      backgroundColor: "#ffffff",
      boxSizing: "border-box" as const,
    },
    // Right column (sidebar)
    rightColumn: {
      width: "35%",
      backgroundColor: "#2D7D7D",
      padding: isPDF ? "0.5in" : "40px",
      color: "#ffffff",
      boxSizing: "border-box" as const,
    },
    // Header styles
    name: {
      fontSize: isPDF ? "24pt" : "32px",
      fontWeight: "bold" as const,
      color: "#333333",
      margin: "0 0 8px 0",
      letterSpacing: "1px",
    },
    jobTitle: {
      fontSize: isPDF ? "12pt" : "16px",
      color: "#2D7D7D",
      margin: "0 0 16px 0",
      fontWeight: "400" as const,
    },
    contactInfo: {
      fontSize: isPDF ? "9pt" : "12px",
      color: "#666666",
      marginBottom: "4px",
      display: "flex",
      alignItems: "center",
    },
    contactIcon: {
      marginRight: "8px",
      fontSize: isPDF ? "8pt" : "11px",
    },
    // Section headers
    sectionHeader: {
      fontSize: isPDF ? "14pt" : "18px",
      fontWeight: "bold" as const,
      color: "#333333",
      margin: "24px 0 12px 0",
      textTransform: "uppercase" as const,
      letterSpacing: "0.5px",
    },
    sidebarSectionHeader: {
      fontSize: isPDF ? "12pt" : "16px",
      fontWeight: "bold" as const,
      color: "#ffffff",
      margin: "24px 0 12px 0",
      textTransform: "uppercase" as const,
      letterSpacing: "0.5px",
    },
    // Content styles
    subsection: {
      marginBottom: isPDF ? "16pt" : "20px",
    },
    experienceTitle: {
      fontSize: isPDF ? "11pt" : "14px",
      fontWeight: "bold" as const,
      color: "#333333",
      margin: "0 0 4px 0",
    },
    company: {
      fontSize: isPDF ? "10pt" : "13px",
      color: "#2D7D7D",
      fontWeight: "600" as const,
      margin: "0 0 4px 0",
    },
    date: {
      fontSize: isPDF ? "9pt" : "12px",
      color: "#666666",
      fontWeight: "500" as const,
    },
    bullet: {
      fontSize: isPDF ? "9pt" : "12px",
      color: "#555555",
      marginLeft: "16px",
      marginBottom: "4px",
      lineHeight: "1.4",
    },
    summary: {
      fontSize: isPDF ? "10pt" : "13px",
      color: "#555555",
      lineHeight: "1.5",
      margin: "0 0 20px 0",
      textAlign: "justify" as const,
    },
    // Sidebar styles
    sidebarItem: {
      marginBottom: "16px",
    },
    sidebarTitle: {
      fontSize: isPDF ? "11pt" : "14px",
      fontWeight: "bold" as const,
      color: "#ffffff",
      margin: "0 0 8px 0",
    },
    sidebarContent: {
      fontSize: isPDF ? "9pt" : "12px",
      color: "#E0F2F2",
      lineHeight: "1.4",
      margin: "0 0 8px 0",
    },
    skillItem: {
      backgroundColor: "rgba(255, 255, 255, 0.1)",
      padding: "4px 8px",
      borderRadius: "12px",
      display: "inline-block",
      margin: "2px 4px 2px 0",
      fontSize: isPDF ? "8pt" : "11px",
      color: "#ffffff",
    },
    achievementItem: {
      display: "flex",
      alignItems: "flex-start",
      marginBottom: "12px",
    },
    achievementIcon: {
      color: "#4FFFB0",
      marginRight: "8px",
      marginTop: "2px",
      fontSize: isPDF ? "8pt" : "11px",
    },
    achievementText: {
      fontSize: isPDF ? "9pt" : "12px",
      color: "#E0F2F2",
      lineHeight: "1.4",
    },
  }

  // Helper function to get the first job title for header
  const getJobTitle = () => {
    if (resumeData.workExperience.length > 0) {
      return resumeData.workExperience[0].jobTitle
    }
    return "Professional"
  }

  return (
    <div style={styles.container}>
      {/* Left Column - Main Content */}
      <div style={styles.leftColumn}>
        {/* Header */}
        <div style={{ marginBottom: "32px" }}>
          <h1 style={styles.name}>{resumeData.personalInfo.fullName || "Your Name"}</h1>
          <div style={styles.jobTitle}>{getJobTitle()}</div>

          {/* Contact Information */}
          <div style={{ marginTop: "16px" }}>
            {resumeData.personalInfo.phone && (
              <div style={styles.contactInfo}>
                <span style={styles.contactIcon}>📞</span>
                {resumeData.personalInfo.phone}
              </div>
            )}
            {resumeData.personalInfo.email && (
              <div style={styles.contactInfo}>
                <span style={styles.contactIcon}>✉️</span>
                {resumeData.personalInfo.email}
              </div>
            )}
            {resumeData.personalInfo.location && (
              <div style={styles.contactInfo}>
                <span style={styles.contactIcon}>📍</span>
                {resumeData.personalInfo.location}
              </div>
            )}
            {resumeData.personalInfo.linkedin && (
              <div style={styles.contactInfo}>
                <span style={styles.contactIcon}>💼</span>
                {resumeData.personalInfo.linkedin}
              </div>
            )}
          </div>
        </div>

        {/* Professional Summary */}
        {resumeData.professionalSummary.summary && (
          <div style={{ marginBottom: "32px" }}>
            <h2 style={styles.sectionHeader}>Summary</h2>
            <p style={styles.summary}>{resumeData.professionalSummary.summary}</p>
          </div>
        )}

        {/* Work Experience */}
        {resumeData.workExperience.length > 0 && (
          <div style={{ marginBottom: "32px" }}>
            <h2 style={styles.sectionHeader}>Experience</h2>
            {resumeData.workExperience.map((exp) => (
              <div key={exp.id} style={styles.subsection}>
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start", marginBottom: "8px" }}>
                  <div style={{ flex: 1 }}>
                    <h3 style={styles.experienceTitle}>{exp.jobTitle}</h3>
                    <div style={styles.company}>{exp.company}</div>
                    <div style={{ ...styles.date, marginTop: "4px" }}>
                      {formatDate(exp.startDate)} - {exp.isCurrentRole ? "Present" : formatDate(exp.endDate)}
                    </div>
                  </div>
                </div>
                {exp.responsibilities
                  .filter((resp) => resp.trim())
                  .map((responsibility, respIndex) => (
                    <div key={respIndex} style={styles.bullet}>
                      • {responsibility}
                    </div>
                  ))}
              </div>
            ))}
          </div>
        )}

        {/* Education */}
        {resumeData.education.length > 0 && (
          <div style={{ marginBottom: "32px" }}>
            <h2 style={styles.sectionHeader}>Education</h2>
            {resumeData.education.map((edu) => (
              <div key={edu.id} style={styles.subsection}>
                <h3 style={styles.experienceTitle}>{edu.degree}</h3>
                <div style={styles.company}>{edu.institution}</div>
                <div style={styles.date}>{formatDate(edu.graduationDate)}</div>
                {edu.gpa && (
                  <div style={{ ...styles.date, marginTop: "4px" }}>GPA: {edu.gpa}</div>
                )}
                {edu.relevantCoursework.length > 0 && (
                  <div style={{ ...styles.summary, marginTop: "8px", marginBottom: "0" }}>
                    <strong>Relevant Coursework:</strong> {edu.relevantCoursework.join(", ")}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Right Column - Sidebar */}
      <div style={styles.rightColumn}>
        {/* Projects */}
        {resumeData.projects.length > 0 && (
          <div style={{ marginBottom: "32px" }}>
            <h2 style={styles.sidebarSectionHeader}>Projects</h2>
            {resumeData.projects.map((project) => (
              <div key={project.id} style={styles.sidebarItem}>
                <h3 style={styles.sidebarTitle}>{project.name}</h3>
                {project.description && (
                  <div style={styles.sidebarContent}>{project.description}</div>
                )}
                {project.technologies.length > 0 && (
                  <div style={{ marginBottom: "8px" }}>
                    {project.technologies.map((tech, techIndex) => (
                      <span key={techIndex} style={styles.skillItem}>
                        {tech}
                      </span>
                    ))}
                  </div>
                )}
                {project.highlights
                  .filter((highlight) => highlight.trim())
                  .slice(0, 2) // Limit to 2 highlights for space
                  .map((highlight, highlightIndex) => (
                    <div key={highlightIndex} style={styles.achievementItem}>
                      <span style={styles.achievementIcon}>✓</span>
                      <span style={styles.achievementText}>{highlight}</span>
                    </div>
                  ))}
              </div>
            ))}
          </div>
        )}

        {/* Key Achievements */}
        {resumeData.workExperience.length > 0 && (
          <div style={{ marginBottom: "32px" }}>
            <h2 style={styles.sidebarSectionHeader}>Key Achievements</h2>
            {resumeData.workExperience
              .flatMap(exp => exp.responsibilities)
              .filter(resp => resp.trim())
              .slice(0, 4) // Show top 4 achievements
              .map((achievement, index) => (
                <div key={index} style={styles.achievementItem}>
                  <span style={styles.achievementIcon}>✓</span>
                  <span style={styles.achievementText}>{achievement}</span>
                </div>
              ))}
          </div>
        )}

        {/* Technical Skills */}
        {resumeData.technicalSkills.length > 0 && (
          <div style={{ marginBottom: "32px" }}>
            <h2 style={styles.sidebarSectionHeader}>Skills</h2>
            {resumeData.technicalSkills.map((category, categoryIndex) => (
              <div key={categoryIndex} style={styles.sidebarItem}>
                <h3 style={styles.sidebarTitle}>{category.category}</h3>
                <div>
                  {category.skills.map((skill, skillIndex) => (
                    <span key={skillIndex} style={styles.skillItem}>
                      {skill.name}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Training/Courses */}
        {resumeData.certifications.length > 0 && (
          <div>
            <h2 style={styles.sidebarSectionHeader}>Training / Courses</h2>
            {resumeData.certifications.map((cert) => (
              <div key={cert.id} style={styles.sidebarItem}>
                <h3 style={styles.sidebarTitle}>{cert.name}</h3>
                <div style={styles.sidebarContent}>
                  Provided by {cert.issuer}. {cert.dateObtained && `Completed ${formatDate(cert.dateObtained)}.`}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
