"use client"

import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { Construction, Settings, User } from "lucide-react"

export default function AccountPage() {
  return (
    <div className="flex flex-col h-full">
      <DashboardHeader 
        title="Account Settings"
        subtitle="Manage your account and preferences"
        showCreateButton={false}
      />
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center max-w-md">
          <div className="w-24 h-24 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Construction className="w-12 h-12 text-orange-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Under Construction</h2>
          <p className="text-gray-600 mb-6">
            Account settings and data management features are coming soon. 
            This section will include user preferences, data export/import, 
            and other account-related functionality.
          </p>
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center space-x-2">
              <User className="w-4 h-4" />
              <span>Profile Management</span>
            </div>
            <div className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>Preferences</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
