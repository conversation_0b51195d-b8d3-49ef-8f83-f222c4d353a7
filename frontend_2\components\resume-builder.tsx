"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Maximize2, Download, Loader2 } from "lucide-react"
import { PersonalInfoForm } from "./forms/personal-info-form"
import { ProfessionalSummaryForm } from "./forms/professional-summary-form"
import { TechnicalSkillsForm } from "./forms/technical-skills-form"
import { WorkExperienceForm } from "./forms/work-experience-form"
import { ProjectsForm } from "./forms/projects-form"
import { EducationForm } from "./forms/education-form"
import { CertificationsForm } from "./forms/certifications-form"
import { ResumePreview } from "./resume-preview"
import { useResume } from "@/context/resume-context"
import { downloadResumePDF } from "@/utils/pdf-download"
import { useToast } from "@/hooks/use-toast"

export function ResumeBuilder() {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const { resumeData } = useResume()
  const { toast } = useToast()

  const handleDownloadPDF = async () => {
    setIsDownloading(true)

    try {
      const success = await downloadResumePDF(resumeData)

      if (success) {
        toast({
          title: "Success!",
          description: "Your resume has been downloaded successfully.",
        })
      } else {
        throw new Error("Failed to generate PDF")
      }
    } catch (error) {
      console.error("PDF download error:", error)
      toast({
        title: "Error",
        description: "Failed to download PDF. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsDownloading(false)
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 bg-white z-50 overflow-auto">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">Resume Preview</h2>
          <div className="flex gap-2">
            <Button onClick={handleDownloadPDF} variant="outline" disabled={isDownloading}>
              {isDownloading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating PDF...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </>
              )}
            </Button>
            <Button onClick={toggleFullscreen} variant="outline">
              Exit Fullscreen
            </Button>
          </div>
        </div>
        <div className="p-8">
          <ResumePreview />
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Panel - Forms */}
      <div className="w-1/2 border-r bg-white">
        <div className="p-6 border-b">
          <h1 className="text-2xl font-bold text-gray-900">AI Tech Resume Builder</h1>
          <p className="text-gray-600 mt-1">Create your professional tech resume</p>
        </div>

        <ScrollArea className="h-[calc(100vh-120px)]">
          <div className="p-6 space-y-6">
            <PersonalInfoForm />
            <ProfessionalSummaryForm />
            <TechnicalSkillsForm />
            <WorkExperienceForm />
            <ProjectsForm />
            <EducationForm />
            <CertificationsForm />
          </div>
        </ScrollArea>
      </div>

      {/* Right Panel - Preview */}
      <div className="w-1/2 bg-gray-100">
        <div className="p-4 border-b bg-white flex justify-between items-center">
          <h2 className="text-lg font-semibold">Live Preview</h2>
          <div className="flex gap-2">
            <Button onClick={handleDownloadPDF} size="sm" disabled={isDownloading}>
              {isDownloading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </>
              )}
            </Button>
            <Button onClick={toggleFullscreen} variant="outline" size="sm">
              <Maximize2 className="w-4 h-4 mr-2" />
              Fullscreen
            </Button>
          </div>
        </div>

        <ScrollArea className="h-[calc(100vh-80px)]">
          <div className="p-6">
            <div className="max-w-[8.5in] mx-auto">
              <ResumePreview />
            </div>
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}
