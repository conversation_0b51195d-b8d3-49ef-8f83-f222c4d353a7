import { type NextRequest, NextResponse } from "next/server"
import puppeteer from "puppeteer"
import type { ResumeData } from "@/types/resume"

export async function POST(request: NextRequest) {
  let browser = null

  try {
    // Parse request body
    const resumeData: ResumeData = await request.json()

    // Validate required data
    if (!resumeData) {
      return NextResponse.json({ error: "No resume data provided" }, { status: 400 })
    }

    // Generate HTML content
    const htmlContent = generateResumeHTML(resumeData)

    // Launch Puppeteer with robust configuration
    browser = await puppeteer.launch({
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
      ],
      timeout: 30000,
    })

    

    const page = await browser.newPage()

    // Set viewport for consistent rendering
    await page.setViewport({
      width: 816, // 8.5 inches at 96 DPI
      height: 1056, // 11 inches at 96 DPI
    })

    // Set content with timeout
    await page.setContent(htmlContent, {
      waitUntil: ["networkidle0", "domcontentloaded"],
      timeout: 15000,
    })

    // Wait a bit more for fonts to fully load
    await new Promise((resolve) => setTimeout(resolve, 1000))
    // await page.waitForTimeout(1000)

  
    // Generate PDF with exact specifications
    const pdfBuffer = await page.pdf({
      format: "letter",
      printBackground: true,
      margin: {
        top: "0in",
        right: "0in",
        bottom: "0in",
        left: "0in",
      },
      preferCSSPageSize: true,
      displayHeaderFooter: false,
    })

    await browser.close()
    browser = null

    // Generate safe filename
    const safeName = resumeData.personalInfo.fullName
      ? resumeData.personalInfo.fullName
          .replace(/[^a-zA-Z0-9\s]/g, "")
          .replace(/\s+/g, "_")
          .substring(0, 50)
      : "resume"

    // Return PDF with proper headers
    const response = new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="${safeName}_Resume.pdf"`,
        "Content-Length": pdfBuffer.length.toString(),
        "Cache-Control": "no-cache",
      },
    })

    return response
  } catch (error) {
    console.error("PDF generation error:", error)

    // Ensure browser is closed even on error
    if (browser) {
      try {
        await browser.close()
      } catch (closeError) {
        console.error("Error closing browser:", closeError)
      }
    }

    return NextResponse.json(
      {
        error: "Failed to generate PDF",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}

function generateResumeHTML(resumeData: ResumeData): string {
  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    try {
      const date = new Date(dateString + "-01")
      return date.toLocaleDateString("en-US", { year: "numeric", month: "short" })
    } catch {
      return dateString
    }
  }

  const escapeHtml = (text: string) => {
    if (!text) return ""
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;")
  }

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume - ${escapeHtml(resumeData.personalInfo.fullName || "Resume")}</title>
    <style>
        @page {
            size: letter;
            margin: 0;
            margin-top: 0.65in;

        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 11pt;
            line-height: 1.15;
            color: #000000;
            background: #ffffff;
            width: 8.5in;
            min-height: 11in;
            margin: 0 auto;
            padding-inline: 0.65in;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        
        .header {
            border-bottom: 2px solid #000000;
            padding-bottom: 12pt;
            margin-bottom: 16pt;
            page-break-inside: avoid;
        }
        
        .name {
            font-size: 18pt;
            font-weight: bold;
            color: #000000;
            margin: 0 0 8pt 0;
            text-align: center;
        }
        
        .contact {
            font-size: 10pt;
            color: #000000;
            text-align: center;
            margin-bottom: 4pt;
        }
        
        .section-header {
            font-size: 12pt;
            font-weight: bold;
            color: #000000;
            margin: 16pt 0 8pt 0;
            border-bottom: 1px solid #000000;
            padding-bottom: 2pt;
            text-transform: uppercase;
            page-break-after: avoid;
        }
        
        .subsection {
            margin-bottom: 12pt;
            page-break-inside: avoid;
        }
        
        .job-title {
            font-size: 11pt;
            font-weight: bold;
            color: #000000;
            margin: 0;
        }
        
        .company {
            font-size: 10pt;
            color: #000000;
            margin: 0;
            font-style: italic;
        }
        
        .date {
            font-size: 10pt;
            color: #000000;
            font-weight: bold;
            float: right;
        }
        
        .bullet {
            font-size: 10pt;
            color: #000000;
            margin-left: 20pt;
            margin-bottom: 2pt;
            line-height: 1.2;
        }
        
        .skill-category {
            font-size: 10pt;
            font-weight: bold;
            color: #000000;
            margin: 0 0 4pt 0;
        }
        
        .skill-list {
            font-size: 10pt;
            color: #000000;
            margin-left: 20pt;
            margin-bottom: 8pt;
        }
        
        .flex-container {
            display: table;
            width: 100%;
            margin-bottom: 4pt;
        }
        
        .flex-left {
            display: table-cell;
            width: 70%;
            vertical-align: top;
        }
        
        .flex-right {
            display: table-cell;
            width: 30%;
            text-align: right;
            vertical-align: top;
        }
        
        .summary {
            font-size: 10pt;
            color: #000000;
            line-height: 1.3;
            margin: 0 0 16pt 0;
            text-align: justify;
        }
        
        .project-links {
            font-size: 9pt;
            color: #000000;
        }
        
        .project-description {
            font-size: 10pt;
            color: #000000;
            margin: 2pt 0 4pt 0;
            font-style: italic;
        }
        
        .project-tech {
            font-size: 9pt;
            color: #000000;
            margin: 2pt 0 4pt 0;
            font-weight: bold;
        }
        
        .coursework {
            font-size: 10pt;
            color: #000000;
            margin: 4pt 0 0 0;
        }
        
        .credential {
            font-size: 9pt;
            color: #000000;
            margin: 0;
        }
        
        .clearfix::after {
            content: "";
            display: table;
            clear: both;
        }

        @media print {
            body { -webkit-print-color-adjust: exact; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="name">${escapeHtml(resumeData.personalInfo.fullName || "Your Name")}</h1>
        <div class="contact">
            ${[
              resumeData.personalInfo.email && escapeHtml(resumeData.personalInfo.email),
              resumeData.personalInfo.phone && escapeHtml(resumeData.personalInfo.phone),
              resumeData.personalInfo.location && escapeHtml(resumeData.personalInfo.location),
            ]
              .filter(Boolean)
              .join(" • ")}
        </div>
        ${
          resumeData.personalInfo.website || resumeData.personalInfo.linkedin || resumeData.personalInfo.github
            ? `<div class="contact">
            ${[
              resumeData.personalInfo.website &&
                escapeHtml(resumeData.personalInfo.website.replace(/^https?:\/\//, "")),
              resumeData.personalInfo.linkedin && `LinkedIn: ${escapeHtml(resumeData.personalInfo.linkedin)}`,
              resumeData.personalInfo.github && `GitHub: ${escapeHtml(resumeData.personalInfo.github)}`,
            ]
              .filter(Boolean)
              .join(" • ")}
        </div>`
            : ""
        }
    </div>

    ${
      resumeData.professionalSummary.summary
        ? `<div>
        <h2 class="section-header">Professional Summary</h2>
        <p class="summary">${escapeHtml(resumeData.professionalSummary.summary)}</p>
    </div>`
        : ""
    }

    ${
      resumeData.technicalSkills.length > 0
        ? `<div>
        <h2 class="section-header">Technical Skills</h2>
        ${resumeData.technicalSkills
          .map(
            (category) => `
            <div class="subsection">
                <div class="skill-category">${escapeHtml(category.category)}:</div>
                <div class="skill-list">
                    ${category.skills.map((skill) => `${escapeHtml(skill.name)} (${escapeHtml(skill.proficiency)})`).join(", ")}
                </div>
            </div>
        `,
          )
          .join("")}
    </div>`
        : ""
    }

    ${
      resumeData.workExperience.length > 0
        ? `<div>
        <h2 class="section-header">Work Experience</h2>
        ${resumeData.workExperience
          .map(
            (exp) => `
            <div class="subsection">
                <div class="flex-container">
                    <div class="flex-left">
                        <h3 class="job-title">${escapeHtml(exp.jobTitle)}</h3>
                        <p class="company">${escapeHtml(exp.company)}, ${escapeHtml(exp.location)}</p>
                    </div>
                    <div class="flex-right">
                        <div class="date">
                            ${formatDate(exp.startDate)} - ${exp.isCurrentRole ? "Present" : formatDate(exp.endDate)}
                        </div>
                    </div>
                </div>
                ${exp.responsibilities
                  .filter((resp) => resp.trim())
                  .map((responsibility) => `<div class="bullet">• ${escapeHtml(responsibility)}</div>`)
                  .join("")}
            </div>
        `,
          )
          .join("")}
    </div>`
        : ""
    }

    ${
      resumeData.projects.length > 0
        ? `<div>
        <h2 class="section-header">Projects</h2>
        ${resumeData.projects
          .map(
            (project) => `
            <div class="subsection">
                <div class="flex-container">
                    <div class="flex-left">
                        <h3 class="job-title">${escapeHtml(project.name)}</h3>
                    </div>
                    <div class="flex-right">
                        <div class="project-links">
                            ${project.liveUrl ? "Live Demo" : ""}
                            ${project.liveUrl && project.githubUrl ? " | " : ""}
                            ${project.githubUrl ? "GitHub" : ""}
                        </div>
                    </div>
                </div>
                ${project.description ? `<p class="project-description">${escapeHtml(project.description)}</p>` : ""}
                ${
                  project.technologies.length > 0
                    ? `<p class="project-tech">Technologies: ${project.technologies.map((tech) => escapeHtml(tech)).join(", ")}</p>`
                    : ""
                }
                ${project.highlights
                  .filter((highlight) => highlight.trim())
                  .map((highlight) => `<div class="bullet">• ${escapeHtml(highlight)}</div>`)
                  .join("")}
            </div>
        `,
          )
          .join("")}
    </div>`
        : ""
    }

    ${
      resumeData.education.length > 0
        ? `<div>
        <h2 class="section-header">Education</h2>
        ${resumeData.education
          .map(
            (edu) => `
            <div class="subsection">
                <div class="flex-container">
                    <div class="flex-left">
                        <h3 class="job-title">${escapeHtml(edu.degree)}</h3>
                        <p class="company">${escapeHtml(edu.institution)}, ${escapeHtml(edu.location)}</p>
                    </div>
                    <div class="flex-right">
                        <div class="date">
                            ${formatDate(edu.graduationDate)}
                            ${edu.gpa ? `<div style="font-size: 9pt; font-weight: normal;">GPA: ${escapeHtml(edu.gpa)}</div>` : ""}
                        </div>
                    </div>
                </div>
                ${
                  edu.relevantCoursework.length > 0
                    ? `<p class="coursework"><strong>Relevant Coursework:</strong> ${edu.relevantCoursework.map((course) => escapeHtml(course)).join(", ")}</p>`
                    : ""
                }
            </div>
        `,
          )
          .join("")}
    </div>`
        : ""
    }

    ${
      resumeData.certifications.length > 0
        ? `<div>
        <h2 class="section-header">Certifications</h2>
        ${resumeData.certifications
          .map(
            (cert) => `
            <div class="subsection">
                <div class="flex-container">
                    <div class="flex-left">
                        <h3 class="job-title">${escapeHtml(cert.name)}</h3>
                        <p class="company">${escapeHtml(cert.issuer)}</p>
                        ${cert.credentialId ? `<p class="credential">Credential ID: ${escapeHtml(cert.credentialId)}</p>` : ""}
                    </div>
                    <div class="flex-right">
                        <div class="date">
                            ${formatDate(cert.dateObtained)}
                            ${
                              cert.expirationDate
                                ? `<div style="font-size: 9pt; font-weight: normal;">Expires: ${formatDate(cert.expirationDate)}</div>`
                                : ""
                            }
                        </div>
                    </div>
                </div>
            </div>
        `,
          )
          .join("")}
    </div>`
        : ""
    }
</body>
</html>`
}
