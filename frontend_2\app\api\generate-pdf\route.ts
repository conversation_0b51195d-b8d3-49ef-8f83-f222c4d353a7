import { type NextRequest, NextResponse } from "next/server"
import puppeteer from "puppeteer"
import type { ResumeData } from "@/types/resume"

export async function POST(request: NextRequest) {
  let browser = null

  try {
    // Parse request body
    const resumeData: ResumeData = await request.json()

    // Validate required data
    if (!resumeData) {
      return NextResponse.json({ error: "No resume data provided" }, { status: 400 })
    }

    // Generate HTML content
    const htmlContent = generateResumeHTML(resumeData)

    // Launch Puppeteer with robust configuration
    browser = await puppeteer.launch({
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
      ],
      timeout: 30000,
    })

    

    const page = await browser.newPage()

    // Set viewport for consistent rendering
    await page.setViewport({
      width: 816, // 8.5 inches at 96 DPI
      height: 1056, // 11 inches at 96 DPI
    })

    // Set content with timeout
    await page.setContent(htmlContent, {
      waitUntil: ["networkidle0", "domcontentloaded"],
      timeout: 15000,
    })

    // Wait a bit more for fonts to fully load
    await new Promise((resolve) => setTimeout(resolve, 1000))
    // await page.waitForTimeout(1000)

  
    // Generate PDF with exact specifications
    const pdfBuffer = await page.pdf({
      format: "letter",
      printBackground: true,
      margin: {
        top: "0in",
        right: "0in",
        bottom: "0in",
        left: "0in",
      },
      preferCSSPageSize: true,
      displayHeaderFooter: false,
    })

    await browser.close()
    browser = null

    // Generate safe filename
    const safeName = resumeData.personalInfo.fullName
      ? resumeData.personalInfo.fullName
          .replace(/[^a-zA-Z0-9\s]/g, "")
          .replace(/\s+/g, "_")
          .substring(0, 50)
      : "resume"

    // Return PDF with proper headers
    const response = new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="${safeName}_Resume.pdf"`,
        "Content-Length": pdfBuffer.length.toString(),
        "Cache-Control": "no-cache",
      },
    })

    return response
  } catch (error) {
    console.error("PDF generation error:", error)

    // Ensure browser is closed even on error
    if (browser) {
      try {
        await browser.close()
      } catch (closeError) {
        console.error("Error closing browser:", closeError)
      }
    }

    return NextResponse.json(
      {
        error: "Failed to generate PDF",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}

function generateResumeHTML(resumeData: ResumeData): string {
  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    try {
      const date = new Date(dateString + "-01")
      return date.toLocaleDateString("en-US", { year: "numeric", month: "short" })
    } catch {
      return dateString
    }
  }

  const escapeHtml = (text: string) => {
    if (!text) return ""
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;")
  }

  // Helper function to get the first job title for header
  const getJobTitle = () => {
    if (resumeData.workExperience.length > 0) {
      return resumeData.workExperience[0].jobTitle
    }
    return "Professional"
  }

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume - ${escapeHtml(resumeData.personalInfo.fullName || "Resume")}</title>
    <style>
        @page {
            size: letter;
            margin: 0;
            margin-top: 0.65in;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 10pt;
            line-height: 1.4;
            color: #333333;
            background: #ffffff;
            width: 8.5in;
            min-height: 11in;
            margin: 0 auto;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            display: flex;
            padding: 0;
        }

        /* Left column (main content) */
        .left-column {
            width: 65%;
            padding: 0.5in;
            background-color: #ffffff;
            box-sizing: border-box;
        }

        /* Right column (sidebar) */
        .right-column {
            width: 35%;
            background-color: #2D7D7D;
            padding: 0.5in;
            color: #ffffff;
            box-sizing: border-box;
        }

        /* Header styles */
        .name {
            font-size: 24pt;
            font-weight: bold;
            color: #333333;
            margin: 0 0 8pt 0;
            letter-spacing: 1pt;
        }

        .job-title {
            font-size: 12pt;
            color: #2D7D7D;
            margin: 0 0 16pt 0;
            font-weight: 400;
        }

        .contact-info {
            font-size: 9pt;
            color: #666666;
            margin-bottom: 4pt;
            display: flex;
            align-items: center;
        }

        .contact-icon {
            margin-right: 8pt;
            font-size: 8pt;
        }

        /* Section headers */
        .section-header {
            font-size: 14pt;
            font-weight: bold;
            color: #333333;
            margin: 24pt 0 12pt 0;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
        }

        .sidebar-section-header {
            font-size: 12pt;
            font-weight: bold;
            color: #ffffff;
            margin: 24pt 0 12pt 0;
            text-transform: uppercase;
            letter-spacing: 0.5pt;
        }

        /* Content styles */
        .subsection {
            margin-bottom: 16pt;
            page-break-inside: avoid;
        }

        .experience-title {
            font-size: 11pt;
            font-weight: bold;
            color: #333333;
            margin: 0 0 4pt 0;
        }

        .company {
            font-size: 10pt;
            color: #2D7D7D;
            font-weight: 600;
            margin: 0 0 4pt 0;
        }

        .date {
            font-size: 9pt;
            color: #666666;
            font-weight: 500;
        }

        .bullet {
            font-size: 9pt;
            color: #555555;
            margin-left: 16pt;
            margin-bottom: 4pt;
            line-height: 1.4;
        }

        .summary {
            font-size: 10pt;
            color: #555555;
            line-height: 1.5;
            margin: 0 0 20pt 0;
            text-align: justify;
        }

        /* Sidebar styles */
        .sidebar-item {
            margin-bottom: 16pt;
        }

        .sidebar-title {
            font-size: 11pt;
            font-weight: bold;
            color: #ffffff;
            margin: 0 0 8pt 0;
        }

        .sidebar-content {
            font-size: 9pt;
            color: #E0F2F2;
            line-height: 1.4;
            margin: 0 0 8pt 0;
        }

        .skill-item {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 4pt 8pt;
            border-radius: 12pt;
            display: inline-block;
            margin: 2pt 4pt 2pt 0;
            font-size: 8pt;
            color: #ffffff;
        }

        .achievement-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12pt;
        }

        .achievement-icon {
            color: #4FFFB0;
            margin-right: 8pt;
            margin-top: 2pt;
            font-size: 8pt;
        }

        .achievement-text {
            font-size: 9pt;
            color: #E0F2F2;
            line-height: 1.4;
        }

        @media print {
            body { -webkit-print-color-adjust: exact; }
        }
    </style>
</head>
<body>
    <!-- Left Column - Main Content -->
    <div class="left-column">
        <!-- Header -->
        <div style="margin-bottom: 32pt;">
            <h1 class="name">${escapeHtml(resumeData.personalInfo.fullName || "Your Name")}</h1>
            <div class="job-title">${escapeHtml(getJobTitle())}</div>

            <!-- Contact Information -->
            <div style="margin-top: 16pt;">
                ${resumeData.personalInfo.phone ? `
                <div class="contact-info">
                    <span class="contact-icon">📞</span>
                    ${escapeHtml(resumeData.personalInfo.phone)}
                </div>` : ""}
                ${resumeData.personalInfo.email ? `
                <div class="contact-info">
                    <span class="contact-icon">✉️</span>
                    ${escapeHtml(resumeData.personalInfo.email)}
                </div>` : ""}
                ${resumeData.personalInfo.location ? `
                <div class="contact-info">
                    <span class="contact-icon">📍</span>
                    ${escapeHtml(resumeData.personalInfo.location)}
                </div>` : ""}
                ${resumeData.personalInfo.linkedin ? `
                <div class="contact-info">
                    <span class="contact-icon">💼</span>
                    ${escapeHtml(resumeData.personalInfo.linkedin)}
                </div>` : ""}
            </div>
        </div>

        <!-- Professional Summary -->
        ${resumeData.professionalSummary.summary ? `
        <div style="margin-bottom: 32pt;">
            <h2 class="section-header">Summary</h2>
            <p class="summary">${escapeHtml(resumeData.professionalSummary.summary)}</p>
        </div>` : ""}

        <!-- Work Experience -->
        ${resumeData.workExperience.length > 0 ? `
        <div style="margin-bottom: 32pt;">
            <h2 class="section-header">Experience</h2>
            ${resumeData.workExperience.map((exp) => `
            <div class="subsection">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8pt;">
                    <div style="flex: 1;">
                        <h3 class="experience-title">${escapeHtml(exp.jobTitle)}</h3>
                        <div class="company">${escapeHtml(exp.company)}</div>
                        <div class="date" style="margin-top: 4pt;">
                            ${formatDate(exp.startDate)} - ${exp.isCurrentRole ? "Present" : formatDate(exp.endDate)}
                        </div>
                    </div>
                </div>
                ${exp.responsibilities
                  .filter((resp) => resp.trim())
                  .map((responsibility) => `<div class="bullet">• ${escapeHtml(responsibility)}</div>`)
                  .join("")}
            </div>`).join("")}
        </div>` : ""}

        <!-- Education -->
        ${resumeData.education.length > 0 ? `
        <div style="margin-bottom: 32pt;">
            <h2 class="section-header">Education</h2>
            ${resumeData.education.map((edu) => `
            <div class="subsection">
                <h3 class="experience-title">${escapeHtml(edu.degree)}</h3>
                <div class="company">${escapeHtml(edu.institution)}</div>
                <div class="date">${formatDate(edu.graduationDate)}</div>
                ${edu.gpa ? `<div class="date" style="margin-top: 4pt;">GPA: ${escapeHtml(edu.gpa)}</div>` : ""}
                ${edu.relevantCoursework.length > 0 ? `
                <div class="summary" style="margin-top: 8pt; margin-bottom: 0;">
                    <strong>Relevant Coursework:</strong> ${edu.relevantCoursework.map(course => escapeHtml(course)).join(", ")}
                </div>` : ""}
            </div>`).join("")}
        </div>` : ""}
    </div>

    <!-- Right Column - Sidebar -->
    <div class="right-column">
        <!-- Projects -->
        ${resumeData.projects.length > 0 ? `
        <div style="margin-bottom: 32pt;">
            <h2 class="sidebar-section-header">Projects</h2>
            ${resumeData.projects.map((project) => `
            <div class="sidebar-item">
                <h3 class="sidebar-title">${escapeHtml(project.name)}</h3>
                ${project.description ? `<div class="sidebar-content">${escapeHtml(project.description)}</div>` : ""}
                ${project.technologies.length > 0 ? `
                <div style="margin-bottom: 8pt;">
                    ${project.technologies.map((tech) => `<span class="skill-item">${escapeHtml(tech)}</span>`).join("")}
                </div>` : ""}
                ${project.highlights
                  .filter((highlight) => highlight.trim())
                  .slice(0, 2)
                  .map((highlight) => `
                <div class="achievement-item">
                    <span class="achievement-icon">✓</span>
                    <span class="achievement-text">${escapeHtml(highlight)}</span>
                </div>`).join("")}
            </div>`).join("")}
        </div>` : ""}

        <!-- Key Achievements -->
        ${resumeData.workExperience.length > 0 ? `
        <div style="margin-bottom: 32pt;">
            <h2 class="sidebar-section-header">Key Achievements</h2>
            ${resumeData.workExperience
              .flatMap(exp => exp.responsibilities)
              .filter(resp => resp.trim())
              .slice(0, 4)
              .map((achievement) => `
            <div class="achievement-item">
                <span class="achievement-icon">✓</span>
                <span class="achievement-text">${escapeHtml(achievement)}</span>
            </div>`).join("")}
        </div>` : ""}

        <!-- Technical Skills -->
        ${resumeData.technicalSkills.length > 0 ? `
        <div style="margin-bottom: 32pt;">
            <h2 class="sidebar-section-header">Skills</h2>
            ${resumeData.technicalSkills.map((category) => `
            <div class="sidebar-item">
                <h3 class="sidebar-title">${escapeHtml(category.category)}</h3>
                <div>
                    ${category.skills.map((skill) => `<span class="skill-item">${escapeHtml(skill.name)}</span>`).join("")}
                </div>
            </div>`).join("")}
        </div>` : ""}

        <!-- Training/Courses -->
        ${resumeData.certifications.length > 0 ? `
        <div>
            <h2 class="sidebar-section-header">Training / Courses</h2>
            ${resumeData.certifications.map((cert) => `
            <div class="sidebar-item">
                <h3 class="sidebar-title">${escapeHtml(cert.name)}</h3>
                <div class="sidebar-content">
                    Provided by ${escapeHtml(cert.issuer)}. ${cert.dateObtained ? `Completed ${formatDate(cert.dateObtained)}.` : ""}
                </div>
            </div>`).join("")}
        </div>` : ""}
    </div>
</body>
</html>`
}
