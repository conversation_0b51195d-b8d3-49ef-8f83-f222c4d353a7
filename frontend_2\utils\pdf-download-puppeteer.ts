"use client"

import type { ResumeData } from "@/types/resume"

export async function downloadResumePDF(resumeData: ResumeData): Promise<boolean> {
  try {
    // Validate resume data
    if (!resumeData || !resumeData.personalInfo) {
      throw new Error("Invalid resume data")
    }

    console.log("Starting PDF generation...")

    const response = await fetch("/api/generate-pdf", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/pdf",
      },
      body: JSON.stringify(resumeData),
    })

    console.log("Response status:", response.status)
    console.log("Response headers:", Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Server error:", errorText)

      let errorMessage = "Failed to generate PDF"
      try {
        const errorJson = JSON.parse(errorText)
        errorMessage = errorJson.error || errorMessage
      } catch {
        // Text response, use as is
        errorMessage = errorText || errorMessage
      }

      throw new Error(`Server error (${response.status}): ${errorMessage}`)
    }

    // Check if response is actually a PDF
    const contentType = response.headers.get("content-type")
    if (!contentType?.includes("application/pdf")) {
      throw new Error(`Invalid response type: ${contentType}`)
    }

    // Get the PDF blob
    const pdfBlob = await response.blob()
    console.log("PDF blob size:", pdfBlob.size)

    if (pdfBlob.size === 0) {
      throw new Error("Received empty PDF file")
    }

    // Create download link
    const url = window.URL.createObjectURL(pdfBlob)

    // Generate safe filename
    const fileName = resumeData.personalInfo.fullName
      ? `${resumeData.personalInfo.fullName.replace(/[^a-zA-Z0-9\s]/g, "").replace(/\s+/g, "_")}_Resume.pdf`
      : "resume.pdf"

    // Create and trigger download
    const link = document.createElement("a")
    link.href = url
    link.download = fileName
    link.style.display = "none"

    document.body.appendChild(link)
    link.click()

    // Cleanup
    setTimeout(() => {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }, 100)

    console.log("PDF download completed successfully")
    return true
  } catch (error) {
    console.error("PDF download error:", error)

    // Re-throw with more context
    if (error instanceof Error) {
      throw new Error(`PDF Download Failed: ${error.message}`)
    } else {
      throw new Error("PDF Download Failed: Unknown error occurred")
    }
  }
}
