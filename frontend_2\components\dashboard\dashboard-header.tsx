"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { useResume } from "@/context/resume-context"
import { useToast } from "@/hooks/use-toast"

interface DashboardHeaderProps {
  title: string
  subtitle?: string
  showCreateButton?: boolean
}

export function DashboardHeader({ 
  title, 
  subtitle, 
  showCreateButton = true 
}: DashboardHeaderProps) {
  const router = useRouter()
  const { createNewResume } = useResume()
  const { toast } = useToast()

  const handleCreateNew = async () => {
    try {
      const newResumeId = await createNewResume("New Resume")
      if (newResumeId) {
        toast({
          title: "Resume Created",
          description: "New resume created successfully. Redirecting to editor...",
        })
        router.push("/d/canvas")
      } else {
        throw new Error("Failed to create resume")
      }
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: "Failed to create new resume. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        {showCreateButton && (
          <Button onClick={handleCreateNew} className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Create New Resume</span>
          </Button>
        )}
      </div>
    </div>
  )
}
