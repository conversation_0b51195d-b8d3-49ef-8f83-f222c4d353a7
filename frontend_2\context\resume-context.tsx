"use client"

import { createContext, useContext, useState, useCallback, type ReactNode } from "react"
import type {
  ResumeData,
  PersonalInfo,
  ProfessionalSummary,
  TechnicalSkill,
  WorkExperience,
  Project,
  Education,
  Certification,
} from "@/types/resume"

interface ResumeContextType {
  resumeData: ResumeData
  updatePersonalInfo: (data: PersonalInfo) => void
  updateProfessionalSummary: (data: ProfessionalSummary) => void
  updateTechnicalSkills: (data: TechnicalSkill[]) => void
  updateWorkExperience: (data: WorkExperience[]) => void
  updateProjects: (data: Project[]) => void
  updateEducation: (data: Education[]) => void
  updateCertifications: (data: Certification[]) => void
}

const ResumeContext = createContext<ResumeContextType | undefined>(undefined)

const initialResumeData: ResumeData = {
  personalInfo: {
    fullName: "",
    email: "",
    phone: "",
    location: "",
    linkedin: "",
    github: "",
    website: "",
  },
  professionalSummary: {
    summary: "",
  },
  technicalSkills: [],
  workExperience: [],
  projects: [],
  education: [],
  certifications: [],
}

export function ResumeProvider({ children }: { children: ReactNode }) {
  const [resumeData, setResumeData] = useState<ResumeData>(initialResumeData)

  const updatePersonalInfo = useCallback((data: PersonalInfo) => {
    setResumeData((prev) => ({ ...prev, personalInfo: data }))
  }, [])

  const updateProfessionalSummary = useCallback((data: ProfessionalSummary) => {
    setResumeData((prev) => ({ ...prev, professionalSummary: data }))
  }, [])

  const updateTechnicalSkills = useCallback((data: TechnicalSkill[]) => {
    setResumeData((prev) => ({ ...prev, technicalSkills: data }))
  }, [])

  const updateWorkExperience = useCallback((data: WorkExperience[]) => {
    setResumeData((prev) => ({ ...prev, workExperience: data }))
  }, [])

  const updateProjects = useCallback((data: Project[]) => {
    setResumeData((prev) => ({ ...prev, projects: data }))
  }, [])

  const updateEducation = useCallback((data: Education[]) => {
    setResumeData((prev) => ({ ...prev, education: data }))
  }, [])

  const updateCertifications = useCallback((data: Certification[]) => {
    setResumeData((prev) => ({ ...prev, certifications: data }))
  }, [])

  return (
    <ResumeContext.Provider
      value={{
        resumeData,
        updatePersonalInfo,
        updateProfessionalSummary,
        updateTechnicalSkills,
        updateWorkExperience,
        updateProjects,
        updateEducation,
        updateCertifications,
      }}
    >
      {children}
    </ResumeContext.Provider>
  )
}

export function useResume() {
  const context = useContext(ResumeContext)
  if (context === undefined) {
    throw new Error("useResume must be used within a ResumeProvider")
  }
  return context
}
