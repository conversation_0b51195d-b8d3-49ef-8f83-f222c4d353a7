"use client"

import { createContext, useContext, useState, useCallback, useEffect, useRef, type ReactNode } from "react"
import type {
  ResumeData,
  PersonalInfo,
  ProfessionalSummary,
  TechnicalSkill,
  WorkExperience,
  Project,
  Education,
  Certification,
  SavedResume,
  ResumeMetadata,
} from "@/types/resume"
import { localStorageService, DEFAULT_RESUME_DATA } from "@/lib/local-storage"

interface ResumeContextType {
  // Current resume data
  resumeData: ResumeData
  currentResumeId: string | null

  // Update functions
  updatePersonalInfo: (data: PersonalInfo) => void
  updateProfessionalSummary: (data: ProfessionalSummary) => void
  updateTechnicalSkills: (data: TechnicalSkill[]) => void
  updateWorkExperience: (data: WorkExperience[]) => void
  updateProjects: (data: Project[]) => void
  updateEducation: (data: Education[]) => void
  updateCertifications: (data: Certification[]) => void

  // Resume management
  saveCurrentResume: (title?: string) => Promise<string | null>
  loadResume: (id: string) => Promise<boolean>
  createNewResume: (title?: string) => Promise<string | null>
  duplicateCurrentResume: () => Promise<string | null>
  deleteResume: (id: string) => Promise<boolean>
  getAllResumes: () => ResumeMetadata[]
  resumeList: ResumeMetadata[] // Add reactive resume list
  refreshResumeList: () => void // Add manual refresh function

  // Auto-save status
  isSaving: boolean
  lastSaved: Date | null
  hasUnsavedChanges: boolean

  // Settings
  autoSaveEnabled: boolean
  toggleAutoSave: () => void
}

const ResumeContext = createContext<ResumeContextType | undefined>(undefined)

export function ResumeProvider({ children }: { children: ReactNode }) {
  // Core state
  const [resumeData, setResumeData] = useState<ResumeData>(DEFAULT_RESUME_DATA)
  const [currentResumeId, setCurrentResumeId] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const [resumeList, setResumeList] = useState<ResumeMetadata[]>([])

  // Refs for auto-save functionality
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastDataRef = useRef<string>("")

  // Function to refresh resume list
  const refreshResumeList = useCallback(() => {
    const allResumes = localStorageService.getAllResumeMetadata()
    setResumeList(allResumes)
  }, [])

  // Initialize from localStorage on mount
  useEffect(() => {
    const settings = localStorageService.getSettings()
    setAutoSaveEnabled(settings.autoSave)

    // Load initial resume list
    refreshResumeList()

    // Try to load current resume or create a new one
    const currentId = localStorageService.getCurrentResumeId()
    if (currentId) {
      const currentResume = localStorageService.getResume(currentId)
      if (currentResume) {
        setResumeData(currentResume.data)
        setCurrentResumeId(currentId)
        setLastSaved(new Date(currentResume.updatedAt))
        lastDataRef.current = JSON.stringify(currentResume.data)
      }
    }
  }, [refreshResumeList])

  // Auto-save functionality
  const performAutoSave = useCallback(async () => {
    if (!autoSaveEnabled || !currentResumeId) return

    const currentDataString = JSON.stringify(resumeData)
    if (currentDataString === lastDataRef.current) return // No changes

    setIsSaving(true)
    try {
      const savedId = localStorageService.saveResume(resumeData, currentResumeId)
      if (savedId) {
        setLastSaved(new Date())
        setHasUnsavedChanges(false)
        lastDataRef.current = currentDataString
      }
    } catch (error) {
      console.error("Auto-save failed:", error)
    } finally {
      setIsSaving(false)
    }
  }, [resumeData, currentResumeId, autoSaveEnabled])

  // Debounced auto-save effect
  useEffect(() => {
    if (!autoSaveEnabled) return

    const currentDataString = JSON.stringify(resumeData)
    if (currentDataString !== lastDataRef.current) {
      setHasUnsavedChanges(true)

      // Clear existing timeout
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }

      // Set new timeout
      autoSaveTimeoutRef.current = setTimeout(() => {
        performAutoSave()
      }, localStorageService.getSettings().autoSaveInterval)
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
    }
  }, [resumeData, performAutoSave, autoSaveEnabled])

  // Update functions with change tracking
  const updatePersonalInfo = useCallback((data: PersonalInfo) => {
    setResumeData((prev) => ({ ...prev, personalInfo: data }))
  }, [])

  const updateProfessionalSummary = useCallback((data: ProfessionalSummary) => {
    setResumeData((prev) => ({ ...prev, professionalSummary: data }))
  }, [])

  const updateTechnicalSkills = useCallback((data: TechnicalSkill[]) => {
    setResumeData((prev) => ({ ...prev, technicalSkills: data }))
  }, [])

  const updateWorkExperience = useCallback((data: WorkExperience[]) => {
    setResumeData((prev) => ({ ...prev, workExperience: data }))
  }, [])

  const updateProjects = useCallback((data: Project[]) => {
    setResumeData((prev) => ({ ...prev, projects: data }))
  }, [])

  const updateEducation = useCallback((data: Education[]) => {
    setResumeData((prev) => ({ ...prev, education: data }))
  }, [])

  const updateCertifications = useCallback((data: Certification[]) => {
    setResumeData((prev) => ({ ...prev, certifications: data }))
  }, [])

  // Resume management functions
  const saveCurrentResume = useCallback(async (title?: string): Promise<string | null> => {
    setIsSaving(true)
    try {
      const savedId = localStorageService.saveResume(resumeData, currentResumeId || undefined, title)
      if (savedId) {
        setCurrentResumeId(savedId)
        setLastSaved(new Date())
        setHasUnsavedChanges(false)
        lastDataRef.current = JSON.stringify(resumeData)

        // Set as current resume if it's a new one
        if (!currentResumeId) {
          localStorageService.setCurrentResume(savedId)
        }

        // Refresh resume list to show updates
        refreshResumeList()
      }
      return savedId
    } catch (error) {
      console.error("Save failed:", error)
      return null
    } finally {
      setIsSaving(false)
    }
  }, [resumeData, currentResumeId, refreshResumeList])

  const loadResume = useCallback(async (id: string): Promise<boolean> => {
    try {
      const resume = localStorageService.getResume(id)
      if (!resume) return false

      setResumeData(resume.data)
      setCurrentResumeId(id)
      setLastSaved(new Date(resume.updatedAt))
      setHasUnsavedChanges(false)
      lastDataRef.current = JSON.stringify(resume.data)

      // Set as current resume
      localStorageService.setCurrentResume(id)
      return true
    } catch (error) {
      console.error("Load failed:", error)
      return false
    }
  }, [])

  const createNewResume = useCallback(async (title?: string): Promise<string | null> => {
    try {
      const newResumeData = { ...DEFAULT_RESUME_DATA }
      const savedId = localStorageService.saveResume(newResumeData, undefined, title)

      if (savedId) {
        setResumeData(newResumeData)
        setCurrentResumeId(savedId)
        setLastSaved(new Date())
        setHasUnsavedChanges(false)
        lastDataRef.current = JSON.stringify(newResumeData)

        // Set as current resume
        localStorageService.setCurrentResume(savedId)

        // Refresh resume list to show new resume
        refreshResumeList()
      }
      return savedId
    } catch (error) {
      console.error("Create new resume failed:", error)
      return null
    }
  }, [refreshResumeList])

  const duplicateCurrentResume = useCallback(async (): Promise<string | null> => {
    if (!currentResumeId) return null

    try {
      const duplicatedId = localStorageService.duplicateResume(currentResumeId)

      // Refresh resume list to show duplicated resume
      if (duplicatedId) {
        refreshResumeList()
      }

      return duplicatedId
    } catch (error) {
      console.error("Duplicate failed:", error)
      return null
    }
  }, [currentResumeId, refreshResumeList])

  const deleteResume = useCallback(async (id: string): Promise<boolean> => {
    try {
      const success = localStorageService.deleteResume(id)

      if (success) {
        // Refresh resume list to remove deleted resume
        refreshResumeList()

        // If we deleted the current resume, load another one or create new
        if (id === currentResumeId) {
          const allResumes = localStorageService.getAllResumeMetadata()
          if (allResumes.length > 0) {
            await loadResume(allResumes[0].id)
          } else {
            await createNewResume()
          }
        }
      }

      return success
    } catch (error) {
      console.error("Delete failed:", error)
      return false
    }
  }, [currentResumeId, loadResume, createNewResume, refreshResumeList])

  const getAllResumes = useCallback((): ResumeMetadata[] => {
    return localStorageService.getAllResumeMetadata()
  }, [])

  const toggleAutoSave = useCallback(() => {
    const newValue = !autoSaveEnabled
    setAutoSaveEnabled(newValue)
    localStorageService.updateSettings({ autoSave: newValue })
  }, [autoSaveEnabled])

  return (
    <ResumeContext.Provider
      value={{
        // Current resume data
        resumeData,
        currentResumeId,

        // Update functions
        updatePersonalInfo,
        updateProfessionalSummary,
        updateTechnicalSkills,
        updateWorkExperience,
        updateProjects,
        updateEducation,
        updateCertifications,

        // Resume management
        saveCurrentResume,
        loadResume,
        createNewResume,
        duplicateCurrentResume,
        deleteResume,
        getAllResumes,
        resumeList,
        refreshResumeList,

        // Auto-save status
        isSaving,
        lastSaved,
        hasUnsavedChanges,

        // Settings
        autoSaveEnabled,
        toggleAutoSave,
      }}
    >
      {children}
    </ResumeContext.Provider>
  )
}

export function useResume() {
  const context = useContext(ResumeContext)
  if (context === undefined) {
    throw new Error("useResume must be used within a ResumeProvider")
  }
  return context
}
