import type React from "react"
import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar"
import { ResumeProvider } from "@/context/resume-context"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ResumeProvider>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        <main className="flex-1 overflow-hidden">
          {children}
        </main>
      </div>
    </ResumeProvider>
  )
}
