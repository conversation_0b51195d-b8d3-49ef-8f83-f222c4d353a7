"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus, Trash2, GripVertical } from "lucide-react"
import { useResume } from "@/context/resume-context"
import type { Certification } from "@/types/resume"

export function CertificationsForm() {
  const { resumeData, updateCertifications } = useResume()

  const addCertification = () => {
    const newCertification: Certification = {
      id: Date.now().toString(),
      name: "",
      issuer: "",
      dateObtained: "",
      expirationDate: "",
      credentialId: "",
    }
    updateCertifications([...resumeData.certifications, newCertification])
  }

  const updateCertification = (index: number, field: keyof Certification, value: any) => {
    const updated = [...resumeData.certifications]
    updated[index] = { ...updated[index], [field]: value }
    updateCertifications(updated)
  }

  const removeCertification = (index: number) => {
    const updated = resumeData.certifications.filter((_, i) => i !== index)
    updateCertifications(updated)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Certifications</CardTitle>
        <Button onClick={addCertification} size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Add Certification
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {resumeData.certifications.map((cert, index) => (
          <div key={cert.id} className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <GripVertical className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium">Certification {index + 1}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeCertification(index)}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label>Certification Name</Label>
                <Input
                  placeholder="AWS Certified Solutions Architect"
                  value={cert.name}
                  onChange={(e) => updateCertification(index, "name", e.target.value)}
                />
              </div>

              <div>
                <Label>Issuing Organization</Label>
                <Input
                  placeholder="Amazon Web Services"
                  value={cert.issuer}
                  onChange={(e) => updateCertification(index, "issuer", e.target.value)}
                />
              </div>

              <div>
                <Label>Date Obtained</Label>
                <Input
                  type="month"
                  value={cert.dateObtained}
                  onChange={(e) => updateCertification(index, "dateObtained", e.target.value)}
                />
              </div>

              <div>
                <Label>Expiration Date (Optional)</Label>
                <Input
                  type="month"
                  value={cert.expirationDate}
                  onChange={(e) => updateCertification(index, "expirationDate", e.target.value)}
                />
              </div>

              <div>
                <Label>Credential ID (Optional)</Label>
                <Input
                  placeholder="ABC123XYZ"
                  value={cert.credentialId}
                  onChange={(e) => updateCertification(index, "credentialId", e.target.value)}
                />
              </div>
            </div>
          </div>
        ))}

        {resumeData.certifications.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No certifications added yet.</p>
            <p className="text-sm">Click "Add Certification" to showcase your credentials.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
