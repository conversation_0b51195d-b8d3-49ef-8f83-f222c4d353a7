"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Plus, Trash2, GripVertical, X } from "lucide-react"
import { useResume } from "@/context/resume-context"
import type { Education } from "@/types/resume"

export function EducationForm() {
  const { resumeData, updateEducation } = useResume()
  const [courseInputs, setCourseInputs] = useState<{ [key: string]: string }>({})

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      degree: "",
      institution: "",
      location: "",
      graduationDate: "",
      gpa: "",
      relevantCoursework: [],
    }
    updateEducation([...resumeData.education, newEducation])
  }

  const updateEducationItem = (index: number, field: keyof Education, value: any) => {
    const updated = [...resumeData.education]
    updated[index] = { ...updated[index], [field]: value }
    updateEducation(updated)
  }

  const removeEducation = (index: number) => {
    const updated = resumeData.education.filter((_, i) => i !== index)
    updateEducation(updated)
  }

  const addCoursework = (eduIndex: number, course: string) => {
    if (!course.trim()) return
    const updated = [...resumeData.education]
    updated[eduIndex].relevantCoursework.push(course.trim())
    updateEducation(updated)

    // Clear the input
    setCourseInputs((prev) => ({ ...prev, [eduIndex]: "" }))
  }

  const removeCoursework = (eduIndex: number, courseIndex: number) => {
    const updated = [...resumeData.education]
    updated[eduIndex].relevantCoursework.splice(courseIndex, 1)
    updateEducation(updated)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Education</CardTitle>
        <Button onClick={addEducation} size="sm">
          <Plus className="w-4 h-4 mr-2" />
          Add Education
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {resumeData.education.map((edu, index) => (
          <div key={edu.id} className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <GripVertical className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium">Education {index + 1}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeEducation(index)}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Degree</Label>
                <Input
                  placeholder="Bachelor of Science in Computer Science"
                  value={edu.degree}
                  onChange={(e) => updateEducationItem(index, "degree", e.target.value)}
                />
              </div>

              <div>
                <Label>Institution</Label>
                <Input
                  placeholder="University of Technology"
                  value={edu.institution}
                  onChange={(e) => updateEducationItem(index, "institution", e.target.value)}
                />
              </div>

              <div>
                <Label>Location</Label>
                <Input
                  placeholder="San Francisco, CA"
                  value={edu.location}
                  onChange={(e) => updateEducationItem(index, "location", e.target.value)}
                />
              </div>

              <div>
                <Label>Graduation Date</Label>
                <Input
                  type="month"
                  value={edu.graduationDate}
                  onChange={(e) => updateEducationItem(index, "graduationDate", e.target.value)}
                />
              </div>

              <div>
                <Label>GPA (Optional)</Label>
                <Input
                  placeholder="3.8/4.0"
                  value={edu.gpa}
                  onChange={(e) => updateEducationItem(index, "gpa", e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label>Relevant Coursework</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {edu.relevantCoursework.map((course, courseIndex) => (
                  <Badge key={courseIndex} variant="secondary" className="flex items-center gap-1">
                    {course}
                    <button
                      onClick={() => removeCoursework(index, courseIndex)}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Add coursework (e.g., Data Structures, Algorithms)"
                  value={courseInputs[index] || ""}
                  onChange={(e) => setCourseInputs((prev) => ({ ...prev, [index]: e.target.value }))}
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      addCoursework(index, courseInputs[index] || "")
                    }
                  }}
                />
                <Button variant="outline" onClick={() => addCoursework(index, courseInputs[index] || "")}>
                  Add
                </Button>
              </div>
            </div>
          </div>
        ))}

        {resumeData.education.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No education added yet.</p>
            <p className="text-sm">Click "Add Education" to include your academic background.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
