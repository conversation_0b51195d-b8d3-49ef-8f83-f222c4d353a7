"use client"

import type React from "react"
import { useState, useRef, useC<PERSON>back, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Maximize2, 
  Download, 
  Loader2, 
  GripVertical, 
  ArrowLeft,
  Save 
} from "lucide-react"
import { PersonalInfoForm } from "@/components/forms/personal-info-form"
import { ProfessionalSummaryForm } from "@/components/forms/professional-summary-form"
import { TechnicalSkillsForm } from "@/components/forms/technical-skills-form"
import { WorkExperienceForm } from "@/components/forms/work-experience-form"
import { ProjectsForm } from "@/components/forms/projects-form"
import { EducationForm } from "@/components/forms/education-form"
import { CertificationsForm } from "@/components/forms/certifications-form"
import { ResumePreview } from "@/components/resume-preview"
import { SaveStatus } from "@/components/save-status"
import { useResume } from "@/context/resume-context"
import { downloadResumePDF } from "@/utils/pdf-download-puppeteer"
import { useToast } from "@/hooks/use-toast"

export function DashboardCanvasBuilder() {
  const router = useRouter()
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const [leftWidth, setLeftWidth] = useState(50) // Percentage
  const [isDragging, setIsDragging] = useState(false)
  const [downloadProgress, setDownloadProgress] = useState("")
  const { 
    resumeData, 
    saveCurrentResume, 
    hasUnsavedChanges,
    currentResumeId 
  } = useResume()
  const { toast } = useToast()
  const containerRef = useRef<HTMLDivElement>(null)

  const handleDownloadPDF = async () => {
    // Validate basic resume data
    if (!resumeData.personalInfo.fullName?.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter your full name before downloading.",
        variant: "destructive",
      })
      return
    }

    setIsDownloading(true)
    setDownloadProgress("Preparing PDF...")

    try {
      setDownloadProgress("Generating PDF on server...")

      const success = await downloadResumePDF(resumeData)

      if (success) {
        setDownloadProgress("Download complete!")
        toast({
          title: "Success!",
          description: "Your resume has been downloaded successfully.",
        })
      } else {
        throw new Error("Download returned false")
      }
    } catch (error) {
      console.error("PDF download error:", error)

      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"

      toast({
        title: "Download Failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsDownloading(false)
      setDownloadProgress("")
    }
  }

  const handleBackToDashboard = async () => {
    if (hasUnsavedChanges) {
      try {
        await saveCurrentResume()
        toast({
          title: "Changes Saved",
          description: "Your changes have been saved automatically.",
        })
      } catch (error) {
        toast({
          title: "Save Failed",
          description: "Failed to save changes. Please try saving manually.",
          variant: "destructive",
        })
        return
      }
    }
    router.push("/d")
  }

  const handleManualSave = async () => {
    try {
      const savedId = await saveCurrentResume()
      if (savedId) {
        toast({
          title: "Resume Saved",
          description: "Your resume has been saved successfully.",
        })
      }
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save resume. Please try again.",
        variant: "destructive",
      })
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !containerRef.current) return

      const containerRect = containerRef.current.getBoundingClientRect()
      const newLeftWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100

      // Constrain between 25% and 75%
      const constrainedWidth = Math.min(Math.max(newLeftWidth, 25), 75)
      setLeftWidth(constrainedWidth)
    },
    [isDragging],
  )

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Add event listeners for mouse move and up
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = "col-resize"
      document.body.style.userSelect = "none"
    } else {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = ""
      document.body.style.userSelect = ""
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = ""
      document.body.style.userSelect = ""
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 bg-white z-50 overflow-auto">
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">Resume Preview</h2>
          <div className="flex gap-2">
            <Button onClick={handleDownloadPDF} variant="outline" disabled={isDownloading}>
              {isDownloading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {downloadProgress || "Generating PDF..."}
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </>
              )}
            </Button>
            <Button onClick={toggleFullscreen} variant="outline">
              Exit Fullscreen
            </Button>
          </div>
        </div>
        <div className="p-8 flex justify-center bg-gray-100">
          <ResumePreview />
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToDashboard}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Dashboard</span>
            </Button>
            <div className="h-6 w-px bg-gray-300" />
            <SaveStatus />
          </div>
          <div className="flex items-center space-x-2">
            {hasUnsavedChanges && (
              <Button
                onClick={handleManualSave}
                size="sm"
                variant="outline"
              >
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div ref={containerRef} className="flex flex-1 bg-gray-50 relative">
        {/* Left Panel - Forms */}
        <div className="bg-white border-r" style={{ width: `${leftWidth}%` }}>
          <ScrollArea className="h-full">
            <div className="p-6 space-y-6">
              <PersonalInfoForm />
              <ProfessionalSummaryForm />
              <TechnicalSkillsForm />
              <WorkExperienceForm />
              <ProjectsForm />
              <EducationForm />
              <CertificationsForm />
            </div>
          </ScrollArea>
        </div>

        {/* Resizer */}
        <div
          className="w-2 bg-gray-300 hover:bg-blue-400 cursor-col-resize flex items-center justify-center relative group transition-colors duration-200 select-none"
          onMouseDown={handleMouseDown}
          style={{
            backgroundColor: isDragging ? "#3b82f6" : undefined,
            width: "8px",
          }}
        >
          <div className="absolute inset-y-0 -left-2 -right-2 flex items-center justify-center">
            <GripVertical className="w-4 h-4 text-gray-600 group-hover:text-white" />
          </div>
        </div>

        {/* Right Panel - Preview */}
        <div className="bg-gray-100" style={{ width: `${100 - leftWidth}%` }}>
          <div className="p-4 border-b bg-white flex justify-between items-center">
            <h2 className="text-lg font-semibold">Live Preview</h2>
            <div className="flex gap-2">
              <Button onClick={handleDownloadPDF} size="sm" disabled={isDownloading}>
                {isDownloading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {downloadProgress || "Generating..."}
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    Download PDF
                  </>
                )}
              </Button>
              <Button onClick={toggleFullscreen} variant="outline" size="sm">
                <Maximize2 className="w-4 h-4 mr-2" />
                Fullscreen
              </Button>
            </div>
          </div>

          <ScrollArea className="h-[calc(100%-80px)]">
            <div className="p-6 flex justify-center">
              <ResumePreview />
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  )
}
